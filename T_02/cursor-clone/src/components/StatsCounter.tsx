"use client";

import { useState, useEffect, useRef } from 'react';

interface StatItem {
  label: string;
  value: number;
  suffix?: string;
  prefix?: string;
  duration?: number;
}

interface StatsCounterProps {
  stats: StatItem[];
  className?: string;
}

export default function StatsCounter({ stats, className = '' }: StatsCounterProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [animatedValues, setAnimatedValues] = useState<number[]>(stats.map(() => 0));
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.3 }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => {
      if (ref.current) {
        observer.unobserve(ref.current);
      }
    };
  }, []);

  useEffect(() => {
    if (!isVisible) return;

    const animateCounters = () => {
      stats.forEach((stat, index) => {
        const duration = stat.duration || 2000;
        const steps = 60;
        const stepValue = stat.value / steps;
        const stepDuration = duration / steps;

        let currentStep = 0;
        const timer = setInterval(() => {
          currentStep++;
          const currentValue = Math.min(stepValue * currentStep, stat.value);
          
          setAnimatedValues(prev => {
            const newValues = [...prev];
            newValues[index] = currentValue;
            return newValues;
          });

          if (currentStep >= steps) {
            clearInterval(timer);
          }
        }, stepDuration);
      });
    };

    animateCounters();
  }, [isVisible, stats]);

  const formatNumber = (value: number) => {
    if (value >= 1000000) {
      return (value / 1000000).toFixed(1) + 'M';
    } else if (value >= 1000) {
      return (value / 1000).toFixed(1) + 'K';
    }
    return Math.round(value).toLocaleString();
  };

  return (
    <div ref={ref} className={`grid grid-cols-2 md:grid-cols-4 gap-8 ${className}`}>
      {stats.map((stat, index) => (
        <div key={index} className="text-center">
          <div className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-2">
            {stat.prefix}
            {formatNumber(animatedValues[index])}
            {stat.suffix}
          </div>
          <div className="text-gray-600 dark:text-gray-400 text-sm md:text-base">
            {stat.label}
          </div>
        </div>
      ))}
    </div>
  );
}

// 预设的统计数据
export const cursorStats: StatItem[] = [
  {
    label: "活跃开发者",
    value: 500000,
    suffix: "+",
    duration: 2500
  },
  {
    label: "代码行数生成",
    value: 10000000,
    suffix: "+",
    duration: 3000
  },
  {
    label: "支持的语言",
    value: 40,
    suffix: "+",
    duration: 1500
  },
  {
    label: "企业客户",
    value: 1000,
    suffix: "+",
    duration: 2000
  }
];

// 带动画的进度条组件
export function AnimatedProgressBar({ 
  value, 
  max = 100, 
  label, 
  className = '' 
}: { 
  value: number; 
  max?: number; 
  label: string; 
  className?: string; 
}) {
  const [animatedValue, setAnimatedValue] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.3 }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => {
      if (ref.current) {
        observer.unobserve(ref.current);
      }
    };
  }, []);

  useEffect(() => {
    if (!isVisible) return;

    const duration = 2000;
    const steps = 60;
    const stepValue = value / steps;
    const stepDuration = duration / steps;

    let currentStep = 0;
    const timer = setInterval(() => {
      currentStep++;
      const currentValue = Math.min(stepValue * currentStep, value);
      setAnimatedValue(currentValue);

      if (currentStep >= steps) {
        clearInterval(timer);
      }
    }, stepDuration);

    return () => clearInterval(timer);
  }, [isVisible, value]);

  const percentage = (animatedValue / max) * 100;

  return (
    <div ref={ref} className={className}>
      <div className="flex justify-between items-center mb-2">
        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
          {label}
        </span>
        <span className="text-sm text-gray-500 dark:text-gray-400">
          {Math.round(animatedValue)}%
        </span>
      </div>
      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
        <div
          className="bg-blue-600 h-2 rounded-full transition-all duration-100 ease-out"
          style={{ width: `${percentage}%` }}
        />
      </div>
    </div>
  );
}
