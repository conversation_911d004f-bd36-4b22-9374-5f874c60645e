"use client";

import { useEffect } from 'react';

// 简单的性能监控和分析
export default function Analytics() {
  useEffect(() => {
    // 监控页面加载性能
    const measurePerformance = () => {
      if (typeof window !== 'undefined' && 'performance' in window) {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        
        if (navigation) {
          const metrics = {
            // 页面加载时间
            pageLoadTime: navigation.loadEventEnd - navigation.fetchStart,
            // DOM 内容加载时间
            domContentLoaded: navigation.domContentLoadedEventEnd - navigation.fetchStart,
            // 首次内容绘制时间
            firstContentfulPaint: 0,
            // 最大内容绘制时间
            largestContentfulPaint: 0,
          };

          // 获取 FCP 和 LCP
          const observer = new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
              if (entry.entryType === 'paint' && entry.name === 'first-contentful-paint') {
                metrics.firstContentfulPaint = entry.startTime;
              }
              if (entry.entryType === 'largest-contentful-paint') {
                metrics.largestContentfulPaint = entry.startTime;
              }
            }
          });

          observer.observe({ entryTypes: ['paint', 'largest-contentful-paint'] });

          // 在开发环境中输出性能指标
          if (process.env.NODE_ENV === 'development') {
            setTimeout(() => {
              console.group('🚀 页面性能指标');
              console.log('页面加载时间:', Math.round(metrics.pageLoadTime), 'ms');
              console.log('DOM 内容加载时间:', Math.round(metrics.domContentLoaded), 'ms');
              if (metrics.firstContentfulPaint) {
                console.log('首次内容绘制:', Math.round(metrics.firstContentfulPaint), 'ms');
              }
              if (metrics.largestContentfulPaint) {
                console.log('最大内容绘制:', Math.round(metrics.largestContentfulPaint), 'ms');
              }
              console.groupEnd();
            }, 2000);
          }
        }
      }
    };

    // 页面加载完成后测量性能
    if (document.readyState === 'complete') {
      measurePerformance();
    } else {
      window.addEventListener('load', measurePerformance);
    }

    // 监控用户交互
    const trackInteraction = (event: Event) => {
      if (process.env.NODE_ENV === 'development') {
        console.log('用户交互:', event.type, event.target);
      }
    };

    // 添加事件监听器
    document.addEventListener('click', trackInteraction);
    document.addEventListener('scroll', trackInteraction, { passive: true });

    return () => {
      window.removeEventListener('load', measurePerformance);
      document.removeEventListener('click', trackInteraction);
      document.removeEventListener('scroll', trackInteraction);
    };
  }, []);

  return null; // 这个组件不渲染任何内容
}

// Web Vitals 监控
export function WebVitals() {
  useEffect(() => {
    const reportWebVitals = (metric: any) => {
      if (process.env.NODE_ENV === 'development') {
        console.log('Web Vitals:', metric);
      }
      
      // 在生产环境中，你可以将这些指标发送到分析服务
      // 例如 Google Analytics, Vercel Analytics 等
    };

    // 动态导入 web-vitals 库（如果安装了的话）
    import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
      getCLS(reportWebVitals);
      getFID(reportWebVitals);
      getFCP(reportWebVitals);
      getLCP(reportWebVitals);
      getTTFB(reportWebVitals);
    }).catch(() => {
      // web-vitals 库未安装，忽略错误
    });
  }, []);

  return null;
}

// 错误边界组件
export function ErrorBoundary({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      console.error('JavaScript 错误:', event.error);
      
      // 在生产环境中，你可以将错误发送到错误监控服务
      // 例如 Sentry, LogRocket 等
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.error('未处理的 Promise 拒绝:', event.reason);
    };

    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);

  return <>{children}</>;
}
