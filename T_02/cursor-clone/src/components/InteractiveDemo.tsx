"use client";

import { useState, useEffect } from 'react';

interface DemoStep {
  id: number;
  title: string;
  description: string;
  code: string;
  suggestion: string;
}

const demoSteps: DemoStep[] = [
  {
    id: 1,
    title: "智能代码补全",
    description: "AI 根据上下文预测你的下一步编辑",
    code: `function calculateTotal(items) {
  return items.`,
    suggestion: "reduce((sum, item) => sum + item.price, 0);"
  },
  {
    id: 2,
    title: "自然语言编程",
    description: "用自然语言描述你想要的功能",
    code: `// 创建一个函数来验证邮箱地址`,
    suggestion: `function validateEmail(email) {
  const regex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;
  return regex.test(email);
}`
  },
  {
    id: 3,
    title: "错误检测与修复",
    description: "AI 自动检测并建议修复代码错误",
    code: `const user = {
  name: "<PERSON>",
  age: 30,
  email: "<EMAIL>"
}

console.log(user.nmae); // 错误：属性名拼写错误`,
    suggestion: "建议修复：将 'nmae' 改为 'name'"
  }
];

export default function InteractiveDemo() {
  const [currentStep, setCurrentStep] = useState(0);
  const [displayedCode, setDisplayedCode] = useState('');
  const [showSuggestion, setShowSuggestion] = useState(false);
  const [isTyping, setIsTyping] = useState(false);

  const currentDemo = demoSteps[currentStep];

  useEffect(() => {
    // 重置状态
    setDisplayedCode('');
    setShowSuggestion(false);
    setIsTyping(true);

    // 模拟打字效果
    const code = currentDemo.code;
    let index = 0;

    const typeInterval = setInterval(() => {
      if (index < code.length) {
        setDisplayedCode(code.slice(0, index + 1));
        index++;
      } else {
        clearInterval(typeInterval);
        setIsTyping(false);
        
        // 显示建议
        setTimeout(() => {
          setShowSuggestion(true);
        }, 500);
      }
    }, 50);

    return () => clearInterval(typeInterval);
  }, [currentStep, currentDemo]);

  const nextStep = () => {
    setCurrentStep((prev) => (prev + 1) % demoSteps.length);
  };

  const prevStep = () => {
    setCurrentStep((prev) => (prev - 1 + demoSteps.length) % demoSteps.length);
  };

  return (
    <div className="bg-white dark:bg-gray-900 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
      {/* Header */}
      <div className="bg-gray-50 dark:bg-gray-800 px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {currentDemo.title}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              {currentDemo.description}
            </p>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={prevStep}
              className="p-2 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
              aria-label="上一步"
            >
              <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {currentStep + 1} / {demoSteps.length}
            </span>
            <button
              onClick={nextStep}
              className="p-2 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
              aria-label="下一步"
            >
              <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Code Editor */}
      <div className="relative">
        <div className="bg-gray-900 p-6 font-mono text-sm">
          {/* Editor Header */}
          <div className="flex items-center gap-2 mb-4">
            <div className="w-3 h-3 bg-red-500 rounded-full"></div>
            <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            <span className="text-gray-400 text-xs ml-4">demo.js</span>
          </div>

          {/* Code Content */}
          <div className="text-gray-300 whitespace-pre-wrap">
            {displayedCode}
            {isTyping && (
              <span className="animate-pulse bg-blue-400 w-2 h-4 inline-block ml-1"></span>
            )}
          </div>

          {/* AI Suggestion */}
          {showSuggestion && (
            <div className="mt-4 p-4 bg-blue-900/20 border border-blue-500/30 rounded-lg animate-fade-in-up">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                <span className="text-blue-400 text-xs font-medium">AI 建议</span>
              </div>
              <div className="text-blue-300 text-sm">
                {currentDemo.suggestion}
              </div>
              <div className="flex items-center gap-4 mt-3">
                <button className="text-xs bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded transition-colors">
                  接受 (Tab)
                </button>
                <button className="text-xs text-gray-400 hover:text-gray-300 transition-colors">
                  忽略 (Esc)
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Progress Indicator */}
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-gray-200 dark:bg-gray-700">
          <div
            className="h-full bg-blue-500 transition-all duration-300 ease-out"
            style={{ width: `${((currentStep + 1) / demoSteps.length) * 100}%` }}
          />
        </div>
      </div>

      {/* Footer */}
      <div className="bg-gray-50 dark:bg-gray-800 px-6 py-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            {demoSteps.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentStep(index)}
                className={`w-2 h-2 rounded-full transition-colors ${
                  index === currentStep
                    ? 'bg-blue-500'
                    : 'bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500'
                }`}
                aria-label={`跳转到步骤 ${index + 1}`}
              />
            ))}
          </div>
          <button
            onClick={nextStep}
            className="text-sm bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            下一个演示
          </button>
        </div>
      </div>
    </div>
  );
}
