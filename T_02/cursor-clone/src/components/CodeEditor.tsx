"use client";

import { useState, useEffect } from 'react';

interface CodeLine {
  number: number;
  content: string;
  type: 'normal' | 'suggestion' | 'error' | 'warning';
}

export default function CodeEditor() {
  const [lines, setLines] = useState<CodeLine[]>([
    { number: 1, content: "import React, { useState, useEffect } from 'react';", type: 'normal' },
    { number: 2, content: "import { motion } from 'framer-motion';", type: 'normal' },
    { number: 3, content: "", type: 'normal' },
    { number: 4, content: "// AI 建议: 添加 TypeScript 类型定义", type: 'suggestion' },
    { number: 5, content: "interface UserProps {", type: 'normal' },
    { number: 6, content: "  name: string;", type: 'normal' },
    { number: 7, content: "  email: string;", type: 'normal' },
    { number: 8, content: "  isActive: boolean;", type: 'normal' },
    { number: 9, content: "}", type: 'normal' },
    { number: 10, content: "", type: 'normal' },
    { number: 11, content: "function UserCard({ name, email, isActive }: UserProps) {", type: 'normal' },
    { number: 12, content: "  const [loading, setLoading] = useState(false);", type: 'normal' },
    { number: 13, content: "  ", type: 'suggestion' },
    { number: 14, content: "  useEffect(() => {", type: 'normal' },
    { number: 15, content: "    // AI 建议: 添加数据获取逻辑", type: 'suggestion' },
    { number: 16, content: "    fetchUserData();", type: 'normal' },
    { number: 17, content: "  }, []);", type: 'normal' },
    { number: 18, content: "", type: 'normal' },
    { number: 19, content: "  return (", type: 'normal' },
    { number: 20, content: "    <motion.div", type: 'normal' },
    { number: 21, content: "      initial={{ opacity: 0, y: 20 }}", type: 'normal' },
    { number: 22, content: "      animate={{ opacity: 1, y: 0 }}", type: 'normal' },
    { number: 23, content: "      className=\"user-card\"", type: 'normal' },
    { number: 24, content: "    >", type: 'normal' },
    { number: 25, content: "      <h3>{name}</h3>", type: 'normal' },
    { number: 26, content: "      <p>{email}</p>", type: 'normal' },
    { number: 27, content: "    </motion.div>", type: 'normal' },
    { number: 28, content: "  );", type: 'normal' },
    { number: 29, content: "}", type: 'normal' },
  ]);

  const [currentLine, setCurrentLine] = useState(13);
  const [isTyping, setIsTyping] = useState(false);
  const [aiSuggestion, setAiSuggestion] = useState("");

  useEffect(() => {
    const interval = setInterval(() => {
      setIsTyping(true);
      
      // 模拟 AI 建议
      const suggestions = [
        "const handleClick = () => {",
        "// 添加错误处理",
        "if (loading) return <Spinner />;",
        "const memoizedValue = useMemo(() => {",
        "// TODO: 优化性能"
      ];
      
      const randomSuggestion = suggestions[Math.floor(Math.random() * suggestions.length)];
      setAiSuggestion(randomSuggestion);
      
      setTimeout(() => {
        setIsTyping(false);
        setAiSuggestion("");
      }, 3000);
    }, 8000);

    return () => clearInterval(interval);
  }, []);

  const getLineColor = (type: string) => {
    switch (type) {
      case 'suggestion':
        return 'text-blue-400 bg-blue-900/20';
      case 'error':
        return 'text-red-400 bg-red-900/20';
      case 'warning':
        return 'text-yellow-400 bg-yellow-900/20';
      default:
        return 'text-gray-300';
    }
  };

  const getSyntaxHighlight = (content: string) => {
    // 简单的语法高亮
    return content
      .replace(/(import|export|from|const|let|var|function|return|if|else|for|while|class|interface|type)/g, '<span class="text-purple-400">$1</span>')
      .replace(/(['"`])(.*?)\1/g, '<span class="text-green-400">$1$2$1</span>')
      .replace(/(\d+)/g, '<span class="text-orange-400">$1</span>')
      .replace(/(\/\/.*)/g, '<span class="text-gray-500">$1</span>')
      .replace(/({|}|\[|\]|\(|\))/g, '<span class="text-yellow-300">$1</span>');
  };

  return (
    <div className="bg-gradient-to-br from-gray-900 via-gray-800 to-black rounded-xl h-64 md:h-96 overflow-hidden shadow-2xl border border-gray-700">
      {/* Editor Header */}
      <div className="flex items-center justify-between px-4 py-2 bg-gray-800 border-b border-gray-700">
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-red-500 rounded-full"></div>
          <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
          <span className="text-gray-400 text-sm ml-4">UserCard.tsx</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
          <span className="text-gray-400 text-xs">AI 正在分析...</span>
        </div>
      </div>

      {/* Editor Content */}
      <div className="flex h-full">
        {/* Line Numbers */}
        <div className="bg-gray-800 px-3 py-4 text-gray-500 text-sm font-mono select-none">
          {lines.map((line) => (
            <div
              key={line.number}
              className={`h-5 flex items-center ${
                line.number === currentLine ? 'text-blue-400' : ''
              }`}
            >
              {line.number}
            </div>
          ))}
        </div>

        {/* Code Content */}
        <div className="flex-1 p-4 font-mono text-sm overflow-auto">
          {lines.map((line) => (
            <div
              key={line.number}
              className={`h-5 flex items-center ${getLineColor(line.type)} ${
                line.number === currentLine ? 'bg-blue-900/10' : ''
              }`}
            >
              <span
                dangerouslySetInnerHTML={{
                  __html: getSyntaxHighlight(line.content)
                }}
              />
              {line.number === currentLine && (
                <span className="ml-1 w-2 h-4 bg-blue-400 animate-pulse"></span>
              )}
            </div>
          ))}
          
          {/* AI Suggestion Popup */}
          {isTyping && aiSuggestion && (
            <div className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-gray-800 border border-gray-600 rounded-lg p-3 shadow-lg animate-fade-in-up">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                <span className="text-xs text-gray-400">AI 建议</span>
              </div>
              <div className="text-sm text-gray-300 font-mono">{aiSuggestion}</div>
              <div className="text-xs text-gray-500 mt-2">按 Tab 键接受</div>
            </div>
          )}
        </div>
      </div>

      {/* Status Bar */}
      <div className="bg-gray-800 px-4 py-1 text-xs text-gray-400 flex justify-between border-t border-gray-700">
        <div className="flex items-center gap-4">
          <span>TypeScript React</span>
          <span>UTF-8</span>
          <span>LF</span>
        </div>
        <div className="flex items-center gap-4">
          <span>第 {currentLine} 行，第 1 列</span>
          <span>选择 0</span>
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>AI 已连接</span>
          </div>
        </div>
      </div>
    </div>
  );
}
