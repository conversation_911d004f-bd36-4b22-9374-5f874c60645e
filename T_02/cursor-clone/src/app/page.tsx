"use client";

import { useState } from "react";

export default function Home() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <nav className="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-md border-b border-gray-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-black rounded-md flex items-center justify-center">
                <span className="text-white font-bold text-sm">C</span>
              </div>
            </div>

            <div className="hidden md:flex items-center space-x-8">
              <a href="/pricing" className="text-gray-700 hover:text-black px-3 py-2 text-sm font-medium">定价</a>
              <a href="/features" className="text-gray-700 hover:text-black px-3 py-2 text-sm font-medium">功能</a>
              <a href="/enterprise" className="text-gray-700 hover:text-black px-3 py-2 text-sm font-medium">企业</a>
              <a href="/blog" className="text-gray-700 hover:text-black px-3 py-2 text-sm font-medium">博客</a>
            </div>

            <div className="hidden md:flex items-center space-x-4">
              <a href="/login" className="text-gray-700 hover:text-black px-3 py-2 text-sm font-medium">登录</a>
              <a href="/downloads" className="bg-black text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-800">下载</a>
            </div>

            <div className="md:hidden">
              <button onClick={() => setIsMenuOpen(!isMenuOpen)} className="text-gray-700 hover:text-black p-2">
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  {isMenuOpen ? (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  ) : (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                  )}
                </svg>
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <div className="pt-32 pb-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center">
            <div className="relative mb-12">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 rounded-3xl opacity-20 blur-3xl"></div>
              <div className="relative bg-gradient-to-br from-gray-50 to-white rounded-3xl p-8 md:p-16 shadow-xl border border-gray-100">
                <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-6">
                  AI 代码编辑器
                </h1>
                <p className="text-xl md:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto">
                  旨在让你获得超凡的生产力， Cursor 是使用 AI 编写代码的最佳方式。
                </p>
                
                <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
                  <button className="bg-black text-white px-8 py-4 rounded-lg text-lg font-medium hover:bg-gray-800 transition-all duration-300 hover:scale-105 shadow-lg">
                    下载 MacOS
                  </button>
                  <a href="/downloads" className="text-gray-600 hover:text-black text-lg underline">
                    所有下载
                  </a>
                </div>

                <div className="bg-gradient-to-br from-gray-900 via-gray-800 to-black rounded-xl h-64 md:h-96 flex items-center justify-center shadow-2xl border border-gray-700">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-white rounded-lg flex items-center justify-center mx-auto mb-4">
                      <span className="text-black font-bold text-xl">C</span>
                    </div>
                    <span className="text-gray-400 text-lg">Cursor 编辑器界面预览</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Company Trust Section */}
      <div className="py-16 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-8">由工程师信任</h2>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-8 items-center">
              {["Johnson & Johnson", "OpenAI", "Stripe", "Samsung", "Instacart", "Perplexity", "Ramp", "Shopify", "US Foods", "MercadoLibre"].map((company, index) => (
                <div key={index} className="text-gray-600 font-medium text-sm md:text-base">
                  {company}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              更快构建软件
            </h2>
            <p className="text-xl text-gray-600 mb-8">
              智能、高速且熟悉，Cursor 是 AI 写代码的最佳选择。
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {[
              { title: "前沿智能", description: "由定制化模型与前沿模型混合驱动，Cursor 既聪明又快速。" },
              { title: "熟悉的体验", description: "一键即可导入所有扩展、主题和快捷键绑定。" },
              { title: "隐私选项", description: "启用隐私模式后，你的代码不会储存于远程。Cursor 已通过 SOC 2 认证。" }
            ].map((feature, index) => (
              <div key={index} className="bg-white p-8 rounded-xl shadow-sm border border-gray-200 hover:shadow-lg transition-all duration-300">
                <h3 className="text-xl font-bold text-gray-900 mb-4">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-black text-white py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <div className="w-16 h-16 bg-white rounded-lg flex items-center justify-center mx-auto mb-6">
              <span className="text-black font-bold text-xl">C</span>
            </div>
            <h2 className="text-3xl md:text-4xl font-bold mb-4">立即试用 Cursor</h2>
            <p className="text-xl text-gray-300 mb-8">免费下载</p>
          </div>

          <div className="grid md:grid-cols-4 gap-8 mb-12">
            <div>
              <h3 className="font-bold mb-4">Product</h3>
              <ul className="space-y-2">
                <li><a href="/pricing" className="text-gray-300 hover:text-white">定价</a></li>
                <li><a href="/features" className="text-gray-300 hover:text-white">Features</a></li>
                <li><a href="/downloads" className="text-gray-300 hover:text-white">下载</a></li>
              </ul>
            </div>
            <div>
              <h3 className="font-bold mb-4">Resources</h3>
              <ul className="space-y-2">
                <li><a href="/docs" className="text-gray-300 hover:text-white">文档</a></li>
                <li><a href="/blog" className="text-gray-300 hover:text-white">Blog</a></li>
                <li><a href="/forum" className="text-gray-300 hover:text-white">论坛</a></li>
              </ul>
            </div>
            <div>
              <h3 className="font-bold mb-4">Company</h3>
              <ul className="space-y-2">
                <li><a href="/about" className="text-gray-300 hover:text-white">公司</a></li>
                <li><a href="/careers" className="text-gray-300 hover:text-white">招聘</a></li>
              </ul>
            </div>
            <div>
              <h3 className="font-bold mb-4">Legal</h3>
              <ul className="space-y-2">
                <li><a href="/terms" className="text-gray-300 hover:text-white">条款</a></li>
                <li><a href="/privacy" className="text-gray-300 hover:text-white">隐私</a></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 pt-8 text-center">
            <div className="text-gray-300">
              © 2025 Made by Anysphere
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
