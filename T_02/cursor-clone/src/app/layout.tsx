import type { Metadata } from "next";
import { Geist, <PERSON>ei<PERSON>_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Cursor - The AI Code Editor",
  description: "旨在让你获得超凡的生产力， Cursor 是使用 AI 编写代码的最佳方式。",
  keywords: ["AI", "代码编辑器", "编程", "开发工具", "人工智能", "代码助手"],
  authors: [{ name: "Anysphere" }],
  viewport: "width=device-width, initial-scale=1",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
