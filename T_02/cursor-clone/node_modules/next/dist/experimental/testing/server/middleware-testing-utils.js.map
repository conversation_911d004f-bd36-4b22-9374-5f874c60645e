{"version": 3, "sources": ["../../../../src/experimental/testing/server/middleware-testing-utils.ts"], "sourcesContent": ["import type { IncomingHttpHeaders } from 'http'\nimport { getMiddlewareMatchers } from '../../../build/analysis/get-page-static-info'\nimport { getMiddlewareRouteMatcher } from '../../../shared/lib/router/utils/middleware-route-matcher'\nimport type { NextConfig } from '../../../server/config-shared'\nimport { parseUrl } from '../../../lib/url'\nimport { constructRequest } from './utils'\nimport type { MiddlewareConfigMatcherInput } from '../../../build/segment-config/middleware/middleware-config'\n\nexport interface MiddlewareSourceConfig {\n  matcher?: MiddlewareConfigMatcherInput\n}\n\n/**\n * Checks whether the middleware config will match the provide URL and request\n * information such as headers and cookies. This function is useful for\n * unit tests to assert that middleware is matching (and therefore executing)\n * only when it should be.\n */\nexport function unstable_doesMiddlewareMatch({\n  config,\n  url,\n  headers,\n  cookies,\n  nextConfig,\n}: {\n  config: MiddlewareSourceConfig\n  url: string\n  headers?: IncomingHttpHeaders\n  cookies?: Record<string, string>\n  nextConfig?: NextConfig\n}): boolean {\n  if (!config.matcher) {\n    return true\n  }\n  const matchers = getMiddlewareMatchers(config.matcher, nextConfig ?? {})\n  const routeMatchFn = getMiddlewareRouteMatcher(matchers)\n  const { pathname, searchParams = new URLSearchParams() } = parseUrl(url) || {}\n  const request = constructRequest({ url, headers, cookies })\n  return routeMatchFn(pathname, request, Object.fromEntries(searchParams))\n}\n"], "names": ["unstable_doesMiddlewareMatch", "config", "url", "headers", "cookies", "nextConfig", "matcher", "matchers", "getMiddlewareMatchers", "routeMatchFn", "getMiddlewareRouteMatcher", "pathname", "searchParams", "URLSearchParams", "parseUrl", "request", "constructRequest", "Object", "fromEntries"], "mappings": ";;;;+BAkBgBA;;;eAAAA;;;mCAjBsB;wCACI;qBAEjB;uBACQ;AAa1B,SAASA,6BAA6B,EAC3CC,MAAM,EACNC,GAAG,EACHC,OAAO,EACPC,OAAO,EACPC,UAAU,EAOX;IACC,IAAI,CAACJ,OAAOK,OAAO,EAAE;QACnB,OAAO;IACT;IACA,MAAMC,WAAWC,IAAAA,wCAAqB,EAACP,OAAOK,OAAO,EAAED,cAAc,CAAC;IACtE,MAAMI,eAAeC,IAAAA,iDAAyB,EAACH;IAC/C,MAAM,EAAEI,QAAQ,EAAEC,eAAe,IAAIC,iBAAiB,EAAE,GAAGC,IAAAA,aAAQ,EAACZ,QAAQ,CAAC;IAC7E,MAAMa,UAAUC,IAAAA,uBAAgB,EAAC;QAAEd;QAAKC;QAASC;IAAQ;IACzD,OAAOK,aAAaE,UAAUI,SAASE,OAAOC,WAAW,CAACN;AAC5D"}