{"version": 3, "sources": ["../../src/trace/upload-trace.ts"], "sourcesContent": ["import { traceId } from './shared'\nimport { Telemetry } from '../telemetry/storage'\n\nexport default function uploadTrace({\n  traceUploadUrl,\n  mode,\n  projectDir,\n  distDir,\n  isTurboSession,\n  sync,\n}: {\n  traceUploadUrl: string\n  mode: 'dev' | 'build'\n  projectDir: string\n  distDir: string\n  isTurboSession: boolean\n  sync?: boolean\n}) {\n  const { NEXT_TRACE_UPLOAD_DEBUG } = process.env\n  const telemetry = new Telemetry({ distDir })\n\n  // Note: cross-spawn is not used here as it causes\n  // a new command window to appear when we don't want it to\n  const child_process =\n    require('child_process') as typeof import('child_process')\n\n  // we use spawnSync when debugging to ensure logs are piped\n  // correctly to stdout/stderr\n  const spawn =\n    NEXT_TRACE_UPLOAD_DEBUG || sync\n      ? child_process.spawnSync\n      : child_process.spawn\n\n  spawn(\n    process.execPath,\n    [\n      require.resolve('./trace-uploader'),\n      traceUploadUrl,\n      mode,\n      projectDir,\n      distDir,\n      String(isTurboSession),\n      traceId,\n      telemetry.anonymousId,\n      telemetry.sessionId,\n    ],\n    {\n      detached: !NEXT_TRACE_UPLOAD_DEBUG,\n      windowsHide: true,\n      shell: false,\n      ...(NEXT_TRACE_UPLOAD_DEBUG\n        ? {\n            stdio: 'inherit',\n          }\n        : {}),\n    }\n  )\n}\n"], "names": ["uploadTrace", "traceUploadUrl", "mode", "projectDir", "distDir", "isTurboSession", "sync", "NEXT_TRACE_UPLOAD_DEBUG", "process", "env", "telemetry", "Telemetry", "child_process", "require", "spawn", "spawnSync", "execPath", "resolve", "String", "traceId", "anonymousId", "sessionId", "detached", "windowsHide", "shell", "stdio"], "mappings": ";;;;+BAGA;;;eAAwBA;;;wBAHA;yBACE;AAEX,SAASA,YAAY,EAClCC,cAAc,EACdC,IAAI,EACJC,UAAU,EACVC,OAAO,EACPC,cAAc,EACdC,IAAI,EAQL;IACC,MAAM,EAAEC,uBAAuB,EAAE,GAAGC,QAAQC,GAAG;IAC/C,MAAMC,YAAY,IAAIC,kBAAS,CAAC;QAAEP;IAAQ;IAE1C,kDAAkD;IAClD,0DAA0D;IAC1D,MAAMQ,gBACJC,QAAQ;IAEV,2DAA2D;IAC3D,6BAA6B;IAC7B,MAAMC,QACJP,2BAA2BD,OACvBM,cAAcG,SAAS,GACvBH,cAAcE,KAAK;IAEzBA,MACEN,QAAQQ,QAAQ,EAChB;QACEH,QAAQI,OAAO,CAAC;QAChBhB;QACAC;QACAC;QACAC;QACAc,OAAOb;QACPc,eAAO;QACPT,UAAUU,WAAW;QACrBV,UAAUW,SAAS;KACpB,EACD;QACEC,UAAU,CAACf;QACXgB,aAAa;QACbC,OAAO;QACP,GAAIjB,0BACA;YACEkB,OAAO;QACT,IACA,CAAC,CAAC;IACR;AAEJ"}