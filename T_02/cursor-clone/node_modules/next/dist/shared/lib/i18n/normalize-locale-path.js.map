{"version": 3, "sources": ["../../../../src/shared/lib/i18n/normalize-locale-path.ts"], "sourcesContent": ["export interface PathLocale {\n  detectedLocale?: string\n  pathname: string\n}\n\n/**\n * For a pathname that may include a locale from a list of locales, it\n * removes the locale from the pathname returning it alongside with the\n * detected locale.\n *\n * @param pathname A pathname that may include a locale.\n * @param locales A list of locales.\n * @returns The detected locale and pathname without locale\n */\nexport function normalizeLocalePath(\n  pathname: string,\n  locales?: string[]\n): PathLocale {\n  let detectedLocale: string | undefined\n  // first item will be empty string from splitting at first char\n  const pathnameParts = pathname.split('/')\n\n  ;(locales || []).some((locale) => {\n    if (\n      pathnameParts[1] &&\n      pathnameParts[1].toLowerCase() === locale.toLowerCase()\n    ) {\n      detectedLocale = locale\n      pathnameParts.splice(1, 1)\n      pathname = pathnameParts.join('/') || '/'\n      return true\n    }\n    return false\n  })\n\n  return {\n    pathname,\n    detectedLocale,\n  }\n}\n"], "names": ["normalizeLocalePath", "pathname", "locales", "detectedLocale", "pathnameParts", "split", "some", "locale", "toLowerCase", "splice", "join"], "mappings": ";;;;+BAcgBA;;;eAAAA;;;AAAT,SAASA,oBACdC,QAAgB,EAChBC,OAAkB;IAElB,IAAIC;IACJ,+DAA+D;IAC/D,MAAMC,gBAAgBH,SAASI,KAAK,CAAC;IAEnCH,CAAAA,WAAW,EAAE,AAAD,EAAGI,IAAI,CAAC,CAACC;QACrB,IACEH,aAAa,CAAC,EAAE,IAChBA,aAAa,CAAC,EAAE,CAACI,WAAW,OAAOD,OAAOC,WAAW,IACrD;YACAL,iBAAiBI;YACjBH,cAAcK,MAAM,CAAC,GAAG;YACxBR,WAAWG,cAAcM,IAAI,CAAC,QAAQ;YACtC,OAAO;QACT;QACA,OAAO;IACT;IAEA,OAAO;QACLT;QACAE;IACF;AACF"}