{"version": 3, "sources": ["../../src/cli/next-lint.ts"], "sourcesContent": ["#!/usr/bin/env node\n\nimport { existsSync } from 'fs'\nimport { isAbsolute, join } from 'path'\n\nimport loadConfig from '../server/config'\nimport { printAndExit } from '../server/lib/utils'\nimport { Telemetry } from '../telemetry/storage'\nimport { green } from '../lib/picocolors'\nimport { ESLINT_DEFAULT_DIRS } from '../lib/constants'\nimport { runLintCheck } from '../lib/eslint/runLintCheck'\nimport { CompileError } from '../lib/compile-error'\nimport { PHASE_PRODUCTION_BUILD } from '../shared/lib/constants'\nimport { eventLintCheckCompleted } from '../telemetry/events'\nimport { getProjectDir } from '../lib/get-project-dir'\nimport { findPagesDir } from '../lib/find-pages-dir'\nimport { verifyTypeScriptSetup } from '../lib/verify-typescript-setup'\n\nexport type NextLintOptions = {\n  cache: boolean\n  cacheLocation?: string\n  cacheStrategy: string\n  config?: string\n  dir?: string[]\n  errorOnUnmatchedPattern?: boolean\n  file?: string[]\n  fix?: boolean\n  fixType?: string\n  format?: string\n  ignore: boolean\n  outputFile?: string\n  quiet?: boolean\n  strict?: boolean\n  // TODO(jiwon): ESLint v9 unsupported options\n  // we currently delete them at `runLintCheck` when used in v9\n  ext: string[]\n  ignorePath?: string\n  reportUnusedDisableDirectivesSeverity: 'error' | 'off' | 'warn'\n  resolvePluginsRelativeTo?: string\n  rulesdir?: string\n  inlineConfig: boolean\n  maxWarnings: number\n}\n\nconst eslintOptions = (\n  options: NextLintOptions,\n  defaultCacheLocation: string\n) => ({\n  overrideConfigFile: options.config || null,\n  extensions: options.ext ?? [],\n  resolvePluginsRelativeTo: options.resolvePluginsRelativeTo || null,\n  rulePaths: options.rulesdir ?? [],\n  fix: options.fix ?? false,\n  fixTypes: options.fixType ?? null,\n  ignorePath: options.ignorePath || null,\n  ignore: options.ignore,\n  allowInlineConfig: options.inlineConfig,\n  reportUnusedDisableDirectives:\n    options.reportUnusedDisableDirectivesSeverity || null,\n  cache: options.cache,\n  cacheLocation: options.cacheLocation || defaultCacheLocation,\n  cacheStrategy: options.cacheStrategy,\n  errorOnUnmatchedPattern: options.errorOnUnmatchedPattern ?? false,\n})\n\nconst nextLint = async (options: NextLintOptions, directory?: string) => {\n  const baseDir = getProjectDir(directory)\n\n  // Check if the provided directory exists\n  if (!existsSync(baseDir)) {\n    printAndExit(`> No such directory exists as the project root: ${baseDir}`)\n  }\n\n  const nextConfig = await loadConfig(PHASE_PRODUCTION_BUILD, baseDir)\n\n  const files = options.file ?? []\n  const dirs = options.dir ?? nextConfig.eslint?.dirs\n  const filesToLint = [...(dirs ?? []), ...files]\n\n  const pathsToLint = (\n    filesToLint.length ? filesToLint : ESLINT_DEFAULT_DIRS\n  ).reduce((res: string[], d: string) => {\n    const currDir = isAbsolute(d) ? d : join(baseDir, d)\n\n    if (!existsSync(currDir)) {\n      return res\n    }\n\n    res.push(currDir)\n    return res\n  }, [])\n\n  const reportErrorsOnly = Boolean(options.quiet)\n  const maxWarnings = options.maxWarnings\n  const formatter = options.format || null\n  const strict = Boolean(options.strict)\n  const outputFile = options.outputFile || null\n\n  const distDir = join(baseDir, nextConfig.distDir)\n  const defaultCacheLocation = join(distDir, 'cache', 'eslint/')\n  const { pagesDir, appDir } = findPagesDir(baseDir)\n\n  await verifyTypeScriptSetup({\n    dir: baseDir,\n    distDir: nextConfig.distDir,\n    intentDirs: [pagesDir, appDir].filter(Boolean) as string[],\n    typeCheckPreflight: false,\n    tsconfigPath: nextConfig.typescript.tsconfigPath,\n    disableStaticImages: nextConfig.images.disableStaticImages,\n    hasAppDir: !!appDir,\n    hasPagesDir: !!pagesDir,\n  })\n\n  runLintCheck(baseDir, pathsToLint, {\n    lintDuringBuild: false,\n    eslintOptions: eslintOptions(options, defaultCacheLocation),\n    reportErrorsOnly,\n    maxWarnings,\n    formatter,\n    outputFile,\n    strict,\n  })\n    .then(async (lintResults) => {\n      const lintOutput =\n        typeof lintResults === 'string' ? lintResults : lintResults?.output\n\n      if (typeof lintResults !== 'string' && lintResults?.eventInfo) {\n        const telemetry = new Telemetry({\n          distDir,\n        })\n        telemetry.record(\n          eventLintCheckCompleted({\n            ...lintResults.eventInfo,\n            buildLint: false,\n          })\n        )\n        await telemetry.flush()\n      }\n\n      if (\n        typeof lintResults !== 'string' &&\n        lintResults?.isError &&\n        lintOutput\n      ) {\n        throw new CompileError(lintOutput)\n      }\n\n      if (lintOutput) {\n        printAndExit(lintOutput, 0)\n      } else if (lintResults && !lintOutput) {\n        printAndExit(green('✔ No ESLint warnings or errors'), 0)\n      } else {\n        // this makes sure we exit 1 after the error from line 116\n        // in packages/next/src/lib/eslint/runLintCheck\n        process.exit(1)\n      }\n    })\n    .catch((err) => {\n      printAndExit(err.message)\n    })\n}\n\nexport { nextLint }\n"], "names": ["nextLint", "eslintOptions", "options", "defaultCacheLocation", "overrideConfigFile", "config", "extensions", "ext", "resolvePluginsRelativeTo", "rulePaths", "rulesdir", "fix", "fixTypes", "fixType", "ignore<PERSON><PERSON>", "ignore", "allowInlineConfig", "inlineConfig", "reportUnusedDisableDirectives", "reportUnusedDisableDirectivesSeverity", "cache", "cacheLocation", "cacheStrategy", "errorOnUnmatchedPattern", "directory", "nextConfig", "baseDir", "getProjectDir", "existsSync", "printAndExit", "loadConfig", "PHASE_PRODUCTION_BUILD", "files", "file", "dirs", "dir", "eslint", "filesToLint", "pathsToLint", "length", "ESLINT_DEFAULT_DIRS", "reduce", "res", "d", "currDir", "isAbsolute", "join", "push", "reportErrorsOnly", "Boolean", "quiet", "maxWarnings", "formatter", "format", "strict", "outputFile", "distDir", "pagesDir", "appDir", "findPagesDir", "verifyTypeScriptSetup", "intentDirs", "filter", "typeCheckPreflight", "tsconfigPath", "typescript", "disableStaticImages", "images", "hasAppDir", "hasPagesDir", "runLintCheck", "lintDuringBuild", "then", "lintResults", "lintOutput", "output", "eventInfo", "telemetry", "Telemetry", "record", "eventLintCheckCompleted", "buildLint", "flush", "isError", "CompileError", "green", "process", "exit", "catch", "err", "message"], "mappings": ";;;;;+BAk<PERSON><PERSON>;;;eAAAA;;;oBAhKkB;sBACM;+DAEV;uBACM;yBACH;4BACJ;2BACc;8BACP;8BACA;4BACU;wBACC;+BACV;8BACD;uCACS;;;;;;AA4BtC,MAAMC,gBAAgB,CACpBC,SACAC,uBACI,CAAA;QACJC,oBAAoBF,QAAQG,MAAM,IAAI;QACtCC,YAAYJ,QAAQK,GAAG,IAAI,EAAE;QAC7BC,0BAA0BN,QAAQM,wBAAwB,IAAI;QAC9DC,WAAWP,QAAQQ,QAAQ,IAAI,EAAE;QACjCC,KAAKT,QAAQS,GAAG,IAAI;QACpBC,UAAUV,QAAQW,OAAO,IAAI;QAC7BC,YAAYZ,QAAQY,UAAU,IAAI;QAClCC,QAAQb,QAAQa,MAAM;QACtBC,mBAAmBd,QAAQe,YAAY;QACvCC,+BACEhB,QAAQiB,qCAAqC,IAAI;QACnDC,OAAOlB,QAAQkB,KAAK;QACpBC,eAAenB,QAAQmB,aAAa,IAAIlB;QACxCmB,eAAepB,QAAQoB,aAAa;QACpCC,yBAAyBrB,QAAQqB,uBAAuB,IAAI;IAC9D,CAAA;AAEA,MAAMvB,WAAW,OAAOE,SAA0BsB;QAWpBC;IAV5B,MAAMC,UAAUC,IAAAA,4BAAa,EAACH;IAE9B,yCAAyC;IACzC,IAAI,CAACI,IAAAA,cAAU,EAACF,UAAU;QACxBG,IAAAA,mBAAY,EAAC,CAAC,gDAAgD,EAAEH,SAAS;IAC3E;IAEA,MAAMD,aAAa,MAAMK,IAAAA,eAAU,EAACC,kCAAsB,EAAEL;IAE5D,MAAMM,QAAQ9B,QAAQ+B,IAAI,IAAI,EAAE;IAChC,MAAMC,OAAOhC,QAAQiC,GAAG,MAAIV,qBAAAA,WAAWW,MAAM,qBAAjBX,mBAAmBS,IAAI;IACnD,MAAMG,cAAc;WAAKH,QAAQ,EAAE;WAAMF;KAAM;IAE/C,MAAMM,cAAc,AAClBD,CAAAA,YAAYE,MAAM,GAAGF,cAAcG,8BAAmB,AAAD,EACrDC,MAAM,CAAC,CAACC,KAAeC;QACvB,MAAMC,UAAUC,IAAAA,gBAAU,EAACF,KAAKA,IAAIG,IAAAA,UAAI,EAACpB,SAASiB;QAElD,IAAI,CAACf,IAAAA,cAAU,EAACgB,UAAU;YACxB,OAAOF;QACT;QAEAA,IAAIK,IAAI,CAACH;QACT,OAAOF;IACT,GAAG,EAAE;IAEL,MAAMM,mBAAmBC,QAAQ/C,QAAQgD,KAAK;IAC9C,MAAMC,cAAcjD,QAAQiD,WAAW;IACvC,MAAMC,YAAYlD,QAAQmD,MAAM,IAAI;IACpC,MAAMC,SAASL,QAAQ/C,QAAQoD,MAAM;IACrC,MAAMC,aAAarD,QAAQqD,UAAU,IAAI;IAEzC,MAAMC,UAAUV,IAAAA,UAAI,EAACpB,SAASD,WAAW+B,OAAO;IAChD,MAAMrD,uBAAuB2C,IAAAA,UAAI,EAACU,SAAS,SAAS;IACpD,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAE,GAAGC,IAAAA,0BAAY,EAACjC;IAE1C,MAAMkC,IAAAA,4CAAqB,EAAC;QAC1BzB,KAAKT;QACL8B,SAAS/B,WAAW+B,OAAO;QAC3BK,YAAY;YAACJ;YAAUC;SAAO,CAACI,MAAM,CAACb;QACtCc,oBAAoB;QACpBC,cAAcvC,WAAWwC,UAAU,CAACD,YAAY;QAChDE,qBAAqBzC,WAAW0C,MAAM,CAACD,mBAAmB;QAC1DE,WAAW,CAAC,CAACV;QACbW,aAAa,CAAC,CAACZ;IACjB;IAEAa,IAAAA,0BAAY,EAAC5C,SAASY,aAAa;QACjCiC,iBAAiB;QACjBtE,eAAeA,cAAcC,SAASC;QACtC6C;QACAG;QACAC;QACAG;QACAD;IACF,GACGkB,IAAI,CAAC,OAAOC;QACX,MAAMC,aACJ,OAAOD,gBAAgB,WAAWA,cAAcA,+BAAAA,YAAaE,MAAM;QAErE,IAAI,OAAOF,gBAAgB,aAAYA,+BAAAA,YAAaG,SAAS,GAAE;YAC7D,MAAMC,YAAY,IAAIC,kBAAS,CAAC;gBAC9BtB;YACF;YACAqB,UAAUE,MAAM,CACdC,IAAAA,+BAAuB,EAAC;gBACtB,GAAGP,YAAYG,SAAS;gBACxBK,WAAW;YACb;YAEF,MAAMJ,UAAUK,KAAK;QACvB;QAEA,IACE,OAAOT,gBAAgB,aACvBA,+BAAAA,YAAaU,OAAO,KACpBT,YACA;YACA,MAAM,IAAIU,0BAAY,CAACV;QACzB;QAEA,IAAIA,YAAY;YACd7C,IAAAA,mBAAY,EAAC6C,YAAY;QAC3B,OAAO,IAAID,eAAe,CAACC,YAAY;YACrC7C,IAAAA,mBAAY,EAACwD,IAAAA,iBAAK,EAAC,mCAAmC;QACxD,OAAO;YACL,0DAA0D;YAC1D,+CAA+C;YAC/CC,QAAQC,IAAI,CAAC;QACf;IACF,GACCC,KAAK,CAAC,CAACC;QACN5D,IAAAA,mBAAY,EAAC4D,IAAIC,OAAO;IAC1B;AACJ"}