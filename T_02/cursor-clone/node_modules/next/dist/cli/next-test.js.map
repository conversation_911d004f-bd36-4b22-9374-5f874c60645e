{"version": 3, "sources": ["../../src/cli/next-test.ts"], "sourcesContent": ["import { writeFileSync } from 'fs'\nimport { getProjectDir } from '../lib/get-project-dir'\nimport { printAndExit } from '../server/lib/utils'\nimport loadConfig from '../server/config'\nimport { PHASE_PRODUCTION_BUILD } from '../shared/lib/constants'\nimport {\n  hasNecessaryDependencies,\n  type MissingDependency,\n} from '../lib/has-necessary-dependencies'\nimport { installDependencies } from '../lib/install-dependencies'\nimport type { NextConfigComplete } from '../server/config-shared'\nimport findUp from 'next/dist/compiled/find-up'\nimport { findPagesDir } from '../lib/find-pages-dir'\nimport { verifyTypeScriptSetup } from '../lib/verify-typescript-setup'\nimport path from 'path'\nimport spawn from 'next/dist/compiled/cross-spawn'\n\nexport interface NextTestOptions {\n  testRunner?: string\n}\n\nexport const SUPPORTED_TEST_RUNNERS_LIST = ['playwright'] as const\nexport type SupportedTestRunners = (typeof SUPPORTED_TEST_RUNNERS_LIST)[number]\n\nconst requiredPackagesByTestRunner: {\n  [k in SupportedTestRunners]: MissingDependency[]\n} = {\n  playwright: [\n    { file: 'playwright', pkg: '@playwright/test', exportsRestrict: false },\n  ],\n}\n\nexport async function nextTest(\n  directory?: string,\n  testRunnerArgs: string[] = [],\n  options: NextTestOptions = {}\n) {\n  // The following mess is in order to support an existing Next.js CLI pattern of optionally, passing a project `directory` as the first argument to execute the command on.\n  // This is problematic for `next test` because as a wrapper around a test runner's `test` command, it needs to pass through any additional arguments and options.\n  // Thus, `directory` could either be a valid Next.js project directory (that the user intends to run `next test` on), or it is the first argument for the test runner.\n  // Unfortunately, since many test runners support passing a path (to a test file or directory containing test files), we must check if `directory` is both a valid path and a valid Next.js project.\n\n  let baseDir, nextConfig\n\n  try {\n    // if directory is `undefined` or a valid path this will succeed.\n    baseDir = getProjectDir(directory, false)\n  } catch (err) {\n    // if that failed, then `directory` is not a valid path, so it must have meant to be the first item for `testRunnerArgs`\n    // @ts-expect-error directory is a string here since `getProjectDir` will succeed if its undefined\n    testRunnerArgs.unshift(directory)\n    // intentionally set baseDir to the resolved '.' path\n    baseDir = getProjectDir()\n  }\n\n  try {\n    // but, `baseDir` might not be a Next.js project directory, it could be a path-like argument for the test runner (i.e. `playwright test test/foo.spec.js`)\n    // if this succeeds, it means that `baseDir` is a Next.js project directory\n    nextConfig = await loadConfig(PHASE_PRODUCTION_BUILD, baseDir)\n  } catch (err) {\n    // if it doesn't, then most likely `baseDir` is not a Next.js project directory\n    // @ts-expect-error directory is a string here since `getProjectDir` will succeed if its undefined\n    testRunnerArgs.unshift(directory)\n    // intentionally set baseDir to the resolved '.' path\n    baseDir = getProjectDir()\n    nextConfig = await loadConfig(PHASE_PRODUCTION_BUILD, baseDir) // let this error bubble up if the `basePath` is still not a valid Next.js project\n  }\n\n  // set the test runner. priority is CLI option > next config > default 'playwright'\n  const configuredTestRunner =\n    options?.testRunner ?? // --test-runner='foo'\n    nextConfig.experimental.defaultTestRunner ?? // { experimental: { defaultTestRunner: 'foo' }}\n    'playwright'\n\n  if (!nextConfig.experimental.testProxy) {\n    return printAndExit(\n      `\\`next experimental-test\\` requires the \\`experimental.testProxy: true\\` configuration option.`\n    )\n  }\n\n  // execute test runner specific function\n  switch (configuredTestRunner) {\n    case 'playwright':\n      return runPlaywright(baseDir, nextConfig, testRunnerArgs)\n    default:\n      return printAndExit(\n        `Test runner ${configuredTestRunner} is not supported.`\n      )\n  }\n}\n\nasync function checkRequiredDeps(\n  baseDir: string,\n  testRunner: SupportedTestRunners\n) {\n  const deps = await hasNecessaryDependencies(\n    baseDir,\n    requiredPackagesByTestRunner[testRunner]\n  )\n  if (deps.missing.length > 0) {\n    await installDependencies(baseDir, deps.missing, true)\n\n    const playwright = spawn(\n      path.join(baseDir, 'node_modules', '.bin', 'playwright'),\n      ['install'],\n      {\n        cwd: baseDir,\n        shell: false,\n        stdio: 'inherit',\n        env: {\n          ...process.env,\n        },\n      }\n    )\n\n    return new Promise((resolve, reject) => {\n      playwright.on('close', (c) => resolve(c))\n      playwright.on('error', (err) => reject(err))\n    })\n  }\n}\n\nasync function runPlaywright(\n  baseDir: string,\n  nextConfig: NextConfigComplete,\n  testRunnerArgs: string[]\n) {\n  await checkRequiredDeps(baseDir, 'playwright')\n\n  const playwrightConfigFile = await findUp(\n    ['playwright.config.js', 'playwright.config.ts'],\n    {\n      cwd: baseDir,\n    }\n  )\n\n  if (!playwrightConfigFile) {\n    const { pagesDir, appDir } = findPagesDir(baseDir)\n\n    const { version: typeScriptVersion } = await verifyTypeScriptSetup({\n      dir: baseDir,\n      distDir: nextConfig.distDir,\n      intentDirs: [pagesDir, appDir].filter(Boolean) as string[],\n      typeCheckPreflight: false,\n      tsconfigPath: nextConfig.typescript.tsconfigPath,\n      disableStaticImages: nextConfig.images.disableStaticImages,\n      hasAppDir: !!appDir,\n      hasPagesDir: !!pagesDir,\n    })\n\n    const isUsingTypeScript = !!typeScriptVersion\n\n    const playwrightConfigFilename = isUsingTypeScript\n      ? 'playwright.config.ts'\n      : 'playwright.config.js'\n\n    writeFileSync(\n      path.join(baseDir, playwrightConfigFilename),\n      defaultPlaywrightConfig(isUsingTypeScript)\n    )\n\n    return printAndExit(\n      `Successfully generated ${playwrightConfigFilename}. Create your first test and then run \\`next experimental-test\\`.`,\n      0\n    )\n  } else {\n    const playwright = spawn(\n      path.join(baseDir, 'node_modules', '.bin', 'playwright'),\n      ['test', ...testRunnerArgs],\n      {\n        cwd: baseDir,\n        shell: false,\n        stdio: 'inherit',\n        env: {\n          ...process.env,\n        },\n      }\n    )\n    return new Promise((resolve, reject) => {\n      playwright.on('close', (c) => resolve(c))\n      playwright.on('error', (err) => reject(err))\n    })\n  }\n}\n\nfunction defaultPlaywrightConfig(typescript: boolean) {\n  const comment = `/*\n * Specify any additional Playwright config options here.\n * They will be merged with Next.js' default Playwright config.\n * You can access the default config by importing \\`defaultPlaywrightConfig\\` from \\`'next/experimental/testmode/playwright'\\`.\n */`\n  return typescript\n    ? `import { defineConfig } from 'next/experimental/testmode/playwright';\\n\\n${comment}\\nexport default defineConfig({});`\n    : `const { defineConfig } = require('next/experimental/testmode/playwright');\\n\\n${comment}\\nmodule.exports = defineConfig({});`\n}\n"], "names": ["SUPPORTED_TEST_RUNNERS_LIST", "nextTest", "requiredPackagesByTestRunner", "playwright", "file", "pkg", "exportsRestrict", "directory", "testRunnerArgs", "options", "baseDir", "nextConfig", "getProjectDir", "err", "unshift", "loadConfig", "PHASE_PRODUCTION_BUILD", "configured<PERSON><PERSON><PERSON><PERSON><PERSON>", "testRunner", "experimental", "defaultTestRunner", "testProxy", "printAndExit", "<PERSON><PERSON><PERSON><PERSON>", "checkRequiredDeps", "deps", "hasNecessaryDependencies", "missing", "length", "installDependencies", "spawn", "path", "join", "cwd", "shell", "stdio", "env", "process", "Promise", "resolve", "reject", "on", "c", "playwright<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "findUp", "pagesDir", "appDir", "findPagesDir", "version", "typeScriptVersion", "verifyTypeScriptSetup", "dir", "distDir", "intentDirs", "filter", "Boolean", "typeCheckPreflight", "tsconfigPath", "typescript", "disableStaticImages", "images", "hasAppDir", "hasPagesDir", "isUsingTypeScript", "playwright<PERSON><PERSON><PERSON>g<PERSON><PERSON><PERSON>", "writeFileSync", "defaultPlaywrightConfig", "comment"], "mappings": ";;;;;;;;;;;;;;;IAqBaA,2BAA2B;eAA3BA;;IAWSC,QAAQ;eAARA;;;oBAhCQ;+BACA;uBACD;+DACN;2BACgB;0CAIhC;qCAC6B;+DAEjB;8BACU;uCACS;6DACrB;mEACC;;;;;;AAMX,MAAMD,8BAA8B;IAAC;CAAa;AAGzD,MAAME,+BAEF;IACFC,YAAY;QACV;YAAEC,MAAM;YAAcC,KAAK;YAAoBC,iBAAiB;QAAM;KACvE;AACH;AAEO,eAAeL,SACpBM,SAAkB,EAClBC,iBAA2B,EAAE,EAC7BC,UAA2B,CAAC,CAAC;IAE7B,0KAA0K;IAC1K,iKAAiK;IACjK,sKAAsK;IACtK,oMAAoM;IAEpM,IAAIC,SAASC;IAEb,IAAI;QACF,iEAAiE;QACjED,UAAUE,IAAAA,4BAAa,EAACL,WAAW;IACrC,EAAE,OAAOM,KAAK;QACZ,wHAAwH;QACxH,kGAAkG;QAClGL,eAAeM,OAAO,CAACP;QACvB,qDAAqD;QACrDG,UAAUE,IAAAA,4BAAa;IACzB;IAEA,IAAI;QACF,0JAA0J;QAC1J,2EAA2E;QAC3ED,aAAa,MAAMI,IAAAA,eAAU,EAACC,iCAAsB,EAAEN;IACxD,EAAE,OAAOG,KAAK;QACZ,+EAA+E;QAC/E,kGAAkG;QAClGL,eAAeM,OAAO,CAACP;QACvB,qDAAqD;QACrDG,UAAUE,IAAAA,4BAAa;QACvBD,aAAa,MAAMI,IAAAA,eAAU,EAACC,iCAAsB,EAAEN,SAAS,kFAAkF;;IACnJ;IAEA,mFAAmF;IACnF,MAAMO,uBACJR,CAAAA,2BAAAA,QAASS,UAAU,KAAI,sBAAsB;IAC7CP,WAAWQ,YAAY,CAACC,iBAAiB,IAAI,gDAAgD;IAC7F;IAEF,IAAI,CAACT,WAAWQ,YAAY,CAACE,SAAS,EAAE;QACtC,OAAOC,IAAAA,mBAAY,EACjB,CAAC,8FAA8F,CAAC;IAEpG;IAEA,wCAAwC;IACxC,OAAQL;QACN,KAAK;YACH,OAAOM,cAAcb,SAASC,YAAYH;QAC5C;YACE,OAAOc,IAAAA,mBAAY,EACjB,CAAC,YAAY,EAAEL,qBAAqB,kBAAkB,CAAC;IAE7D;AACF;AAEA,eAAeO,kBACbd,OAAe,EACfQ,UAAgC;IAEhC,MAAMO,OAAO,MAAMC,IAAAA,kDAAwB,EACzChB,SACAR,4BAA4B,CAACgB,WAAW;IAE1C,IAAIO,KAAKE,OAAO,CAACC,MAAM,GAAG,GAAG;QAC3B,MAAMC,IAAAA,wCAAmB,EAACnB,SAASe,KAAKE,OAAO,EAAE;QAEjD,MAAMxB,aAAa2B,IAAAA,mBAAK,EACtBC,aAAI,CAACC,IAAI,CAACtB,SAAS,gBAAgB,QAAQ,eAC3C;YAAC;SAAU,EACX;YACEuB,KAAKvB;YACLwB,OAAO;YACPC,OAAO;YACPC,KAAK;gBACH,GAAGC,QAAQD,GAAG;YAChB;QACF;QAGF,OAAO,IAAIE,QAAQ,CAACC,SAASC;YAC3BrC,WAAWsC,EAAE,CAAC,SAAS,CAACC,IAAMH,QAAQG;YACtCvC,WAAWsC,EAAE,CAAC,SAAS,CAAC5B,MAAQ2B,OAAO3B;QACzC;IACF;AACF;AAEA,eAAeU,cACbb,OAAe,EACfC,UAA8B,EAC9BH,cAAwB;IAExB,MAAMgB,kBAAkBd,SAAS;IAEjC,MAAMiC,uBAAuB,MAAMC,IAAAA,eAAM,EACvC;QAAC;QAAwB;KAAuB,EAChD;QACEX,KAAKvB;IACP;IAGF,IAAI,CAACiC,sBAAsB;QACzB,MAAM,EAAEE,QAAQ,EAAEC,MAAM,EAAE,GAAGC,IAAAA,0BAAY,EAACrC;QAE1C,MAAM,EAAEsC,SAASC,iBAAiB,EAAE,GAAG,MAAMC,IAAAA,4CAAqB,EAAC;YACjEC,KAAKzC;YACL0C,SAASzC,WAAWyC,OAAO;YAC3BC,YAAY;gBAACR;gBAAUC;aAAO,CAACQ,MAAM,CAACC;YACtCC,oBAAoB;YACpBC,cAAc9C,WAAW+C,UAAU,CAACD,YAAY;YAChDE,qBAAqBhD,WAAWiD,MAAM,CAACD,mBAAmB;YAC1DE,WAAW,CAAC,CAACf;YACbgB,aAAa,CAAC,CAACjB;QACjB;QAEA,MAAMkB,oBAAoB,CAAC,CAACd;QAE5B,MAAMe,2BAA2BD,oBAC7B,yBACA;QAEJE,IAAAA,iBAAa,EACXlC,aAAI,CAACC,IAAI,CAACtB,SAASsD,2BACnBE,wBAAwBH;QAG1B,OAAOzC,IAAAA,mBAAY,EACjB,CAAC,uBAAuB,EAAE0C,yBAAyB,iEAAiE,CAAC,EACrH;IAEJ,OAAO;QACL,MAAM7D,aAAa2B,IAAAA,mBAAK,EACtBC,aAAI,CAACC,IAAI,CAACtB,SAAS,gBAAgB,QAAQ,eAC3C;YAAC;eAAWF;SAAe,EAC3B;YACEyB,KAAKvB;YACLwB,OAAO;YACPC,OAAO;YACPC,KAAK;gBACH,GAAGC,QAAQD,GAAG;YAChB;QACF;QAEF,OAAO,IAAIE,QAAQ,CAACC,SAASC;YAC3BrC,WAAWsC,EAAE,CAAC,SAAS,CAACC,IAAMH,QAAQG;YACtCvC,WAAWsC,EAAE,CAAC,SAAS,CAAC5B,MAAQ2B,OAAO3B;QACzC;IACF;AACF;AAEA,SAASqD,wBAAwBR,UAAmB;IAClD,MAAMS,UAAU,CAAC;;;;GAIhB,CAAC;IACF,OAAOT,aACH,CAAC,yEAAyE,EAAES,QAAQ,kCAAkC,CAAC,GACvH,CAAC,8EAA8E,EAAEA,QAAQ,oCAAoC,CAAC;AACpI"}