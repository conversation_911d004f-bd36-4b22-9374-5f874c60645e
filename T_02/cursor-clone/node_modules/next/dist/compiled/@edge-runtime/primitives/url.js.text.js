module.exports = "\"use strict\";var yt=Object.defineProperty,Le=Object.getOwnPropertyDescriptor,Ae=Object.getOwnPropertyNames,Te=Object.prototype.hasOwnProperty,h=(t,e)=>yt(t,\"name\",{value:e,configurable:!0}),Ue=(t,e)=>{for(var s in e)yt(t,s,{get:e[s],enumerable:!0})},Me=(t,e,s,r)=>{if(e&&typeof e==\"object\"||typeof e==\"function\")for(let c of Ae(e))!Te.call(t,c)&&c!==s&&yt(t,c,{get:()=>e[c],enumerable:!(r=Le(e,c))||r.enumerable});return t},Ie=t=>Me(yt({},\"__esModule\",{value:!0}),t),Ht=(t,e,s)=>{if(!e.has(t))throw TypeError(\"Cannot \"+s)},i=(t,e,s)=>(Ht(t,e,\"read from private field\"),s?s.call(t):e.get(t)),p=(t,e,s)=>{if(e.has(t))throw TypeError(\"Cannot add the same private member more than once\");e instanceof WeakSet?e.add(t):e.set(t,s)},v=(t,e,s,r)=>(Ht(t,e,\"write to private field\"),r?r.call(t,s):e.set(t,s),s),a=(t,e,s)=>(Ht(t,e,\"access private method\"),s),oe={};Ue(oe,{URLPattern:()=>Oe});module.exports=Ie(oe);var xt,ut=(xt=class{type=3;name=\"\";prefix=\"\";value=\"\";suffix=\"\";modifier=3;constructor(t,e,s,r,c,f){this.type=t,this.name=e,this.prefix=s,this.value=r,this.suffix=c,this.modifier=f}hasCustomName(){return this.name!==\"\"&&typeof this.name!=\"number\"}},h(xt,\"R\"),xt),De=/[$_\\p{ID_Start}]/u,Ne=/[$_\\u200C\\u200D\\p{ID_Continue}]/u,Dt=\".*\";function le(t,e){return(e?/^[\\x00-\\xFF]*$/:/^[\\x00-\\x7F]*$/).test(t)}h(le,\"Re\");function Ft(t,e=!1){let s=[],r=0;for(;r<t.length;){let c=t[r],f=h(function(l){if(!e)throw new TypeError(l);s.push({type:\"INVALID_CHAR\",index:r,value:t[r++]})},\"c\");if(c===\"*\"){s.push({type:\"ASTERISK\",index:r,value:t[r++]});continue}if(c===\"+\"||c===\"?\"){s.push({type:\"OTHER_MODIFIER\",index:r,value:t[r++]});continue}if(c===\"\\\\\"){s.push({type:\"ESCAPED_CHAR\",index:r++,value:t[r++]});continue}if(c===\"{\"){s.push({type:\"OPEN\",index:r,value:t[r++]});continue}if(c===\"}\"){s.push({type:\"CLOSE\",index:r,value:t[r++]});continue}if(c===\":\"){let l=\"\",n=r+1;for(;n<t.length;){let u=t.substr(n,1);if(n===r+1&&De.test(u)||n!==r+1&&Ne.test(u)){l+=t[n++];continue}break}if(!l){f(`Missing parameter name at ${r}`);continue}s.push({type:\"NAME\",index:r,value:l}),r=n;continue}if(c===\"(\"){let l=1,n=\"\",u=r+1,o=!1;if(t[u]===\"?\"){f(`Pattern cannot start with \"?\" at ${u}`);continue}for(;u<t.length;){if(!le(t[u],!1)){f(`Invalid character '${t[u]}' at ${u}.`),o=!0;break}if(t[u]===\"\\\\\"){n+=t[u++]+t[u++];continue}if(t[u]===\")\"){if(l--,l===0){u++;break}}else if(t[u]===\"(\"&&(l++,t[u+1]!==\"?\")){f(`Capturing groups are not allowed at ${u}`),o=!0;break}n+=t[u++]}if(o)continue;if(l){f(`Unbalanced pattern at ${r}`);continue}if(!n){f(`Missing pattern at ${r}`);continue}s.push({type:\"REGEX\",index:r,value:n}),r=u;continue}s.push({type:\"CHAR\",index:r,value:t[r++]})}return s.push({type:\"END\",index:r,value:\"\"}),s}h(Ft,\"v\");function Gt(t,e={}){let s=Ft(t);e.delimiter??=\"/#?\",e.prefixes??=\"./\";let r=`[^${R(e.delimiter)}]+?`,c=[],f=0,l=0,n=\"\",u=new Set,o=h(w=>{if(l<s.length&&s[l].type===w)return s[l++].value},\"a\"),x=h(()=>o(\"OTHER_MODIFIER\")??o(\"ASTERISK\"),\"f\"),L=h(w=>{let b=o(w);if(b!==void 0)return b;let{type:$,index:J}=s[l];throw new TypeError(`Unexpected ${$} at ${J}, expected ${w}`)},\"d\"),z=h(()=>{let w=\"\",b;for(;b=o(\"CHAR\")??o(\"ESCAPED_CHAR\");)w+=b;return w},\"T\"),Pe=h(w=>w,\"Se\"),ft=e.encodePart||Pe,pt=\"\",bt=h(w=>{pt+=w},\"U\"),kt=h(()=>{pt.length&&(c.push(new ut(3,\"\",\"\",ft(pt),\"\",3)),pt=\"\")},\"$\"),Bt=h((w,b,$,J,I)=>{let O=3;switch(I){case\"?\":O=1;break;case\"*\":O=0;break;case\"+\":O=2;break}if(!b&&!$&&O===3){bt(w);return}if(kt(),!b&&!$){if(!w)return;c.push(new ut(3,\"\",\"\",ft(w),\"\",O));return}let S;$?$===\"*\"?S=Dt:S=$:S=r;let et=2;S===r?(et=1,S=\"\"):S===Dt&&(et=0,S=\"\");let D;if(b?D=b:$&&(D=f++),u.has(D))throw new TypeError(`Duplicate name '${D}'.`);u.add(D),c.push(new ut(et,D,ft(w),S,ft(J),O))},\"V\");for(;l<s.length;){let w=o(\"CHAR\"),b=o(\"NAME\"),$=o(\"REGEX\");if(!b&&!$&&($=o(\"ASTERISK\")),b||$){let I=w??\"\";e.prefixes.indexOf(I)===-1&&(bt(I),I=\"\"),kt();let O=x();Bt(I,b,$,\"\",O);continue}let J=w??o(\"ESCAPED_CHAR\");if(J){bt(J);continue}if(o(\"OPEN\")){let I=z(),O=o(\"NAME\"),S=o(\"REGEX\");!O&&!S&&(S=o(\"ASTERISK\"));let et=z();L(\"CLOSE\");let D=x();Bt(I,O,S,et,D);continue}kt(),L(\"END\")}return c}h(Gt,\"D\");function R(t){return t.replace(/([.+*?^${}()[\\]|/\\\\])/g,\"\\\\$1\")}h(R,\"S\");function Nt(t){return t&&t.ignoreCase?\"ui\":\"u\"}h(Nt,\"X\");function ce(t,e,s){return Kt(Gt(t,s),e,s)}h(ce,\"Z\");function V(t){switch(t){case 0:return\"*\";case 1:return\"?\";case 2:return\"+\";case 3:return\"\"}}h(V,\"k\");function Kt(t,e,s={}){s.delimiter??=\"/#?\",s.prefixes??=\"./\",s.sensitive??=!1,s.strict??=!1,s.end??=!0,s.start??=!0,s.endsWith=\"\";let r=s.start?\"^\":\"\";for(let n of t){if(n.type===3){n.modifier===3?r+=R(n.value):r+=`(?:${R(n.value)})${V(n.modifier)}`;continue}e&&e.push(n.name);let u=`[^${R(s.delimiter)}]+?`,o=n.value;if(n.type===1?o=u:n.type===0&&(o=Dt),!n.prefix.length&&!n.suffix.length){n.modifier===3||n.modifier===1?r+=`(${o})${V(n.modifier)}`:r+=`((?:${o})${V(n.modifier)})`;continue}if(n.modifier===3||n.modifier===1){r+=`(?:${R(n.prefix)}(${o})${R(n.suffix)})`,r+=V(n.modifier);continue}r+=`(?:${R(n.prefix)}`,r+=`((?:${o})(?:`,r+=R(n.suffix),r+=R(n.prefix),r+=`(?:${o}))*)${R(n.suffix)})`,n.modifier===0&&(r+=\"?\")}let c=`[${R(s.endsWith)}]|$`,f=`[${R(s.delimiter)}]`;if(s.end)return s.strict||(r+=`${f}?`),s.endsWith.length?r+=`(?=${c})`:r+=\"$\",new RegExp(r,Nt(s));s.strict||(r+=`(?:${f}(?=${c}))?`);let l=!1;if(t.length){let n=t[t.length-1];n.type===3&&n.modifier===3&&(l=s.delimiter.indexOf(n)>-1)}return l||(r+=`(?=${f}|${c})`),new RegExp(r,Nt(s))}h(Kt,\"F\");var H={delimiter:\"\",prefixes:\"\",sensitive:!0,strict:!0},je={delimiter:\".\",prefixes:\"\",sensitive:!0,strict:!0},He={delimiter:\"/\",prefixes:\"/\",sensitive:!0,strict:!0};function ue(t,e){return t.length?t[0]===\"/\"?!0:!e||t.length<2?!1:(t[0]==\"\\\\\"||t[0]==\"{\")&&t[1]==\"/\":!1}h(ue,\"J\");function Xt(t,e){return t.startsWith(e)?t.substring(e.length,t.length):t}h(Xt,\"Q\");function fe(t,e){return t.endsWith(e)?t.substr(0,t.length-e.length):t}h(fe,\"Ee\");function Vt(t){return!t||t.length<2?!1:t[0]===\"[\"||(t[0]===\"\\\\\"||t[0]===\"{\")&&t[1]===\"[\"}h(Vt,\"W\");var pe=[\"ftp\",\"file\",\"http\",\"https\",\"ws\",\"wss\"];function zt(t){if(!t)return!0;for(let e of pe)if(t.test(e))return!0;return!1}h(zt,\"N\");function de(t,e){if(t=Xt(t,\"#\"),e||t===\"\")return t;let s=new URL(\"https://example.com\");return s.hash=t,s.hash?s.hash.substring(1,s.hash.length):\"\"}h(de,\"te\");function me(t,e){if(t=Xt(t,\"?\"),e||t===\"\")return t;let s=new URL(\"https://example.com\");return s.search=t,s.search?s.search.substring(1,s.search.length):\"\"}h(me,\"re\");function we(t,e){return e||t===\"\"?t:Vt(t)?Qt(t):Zt(t)}h(we,\"ne\");function ge(t,e){if(e||t===\"\")return t;let s=new URL(\"https://example.com\");return s.password=t,s.password}h(ge,\"se\");function ve(t,e){if(e||t===\"\")return t;let s=new URL(\"https://example.com\");return s.username=t,s.username}h(ve,\"ie\");function ye(t,e,s){if(s||t===\"\")return t;if(e&&!pe.includes(e))return new URL(`${e}:${t}`).pathname;let r=t[0]==\"/\";return t=new URL(r?t:\"/-\"+t,\"https://example.com\").pathname,r||(t=t.substring(2,t.length)),t}h(ye,\"ae\");function _e(t,e,s){return Jt(e)===t&&(t=\"\"),s||t===\"\"?t:qt(t)}h(_e,\"oe\");function be(t,e){return t=fe(t,\":\"),e||t===\"\"?t:_t(t)}h(be,\"ce\");function Jt(t){switch(t){case\"ws\":case\"http\":return\"80\";case\"wws\":case\"https\":return\"443\";case\"ftp\":return\"21\";default:return\"\"}}h(Jt,\"_\");function _t(t){if(t===\"\")return t;if(/^[-+.A-Za-z0-9]*$/.test(t))return t.toLowerCase();throw new TypeError(`Invalid protocol '${t}'.`)}h(_t,\"y\");function ke(t){if(t===\"\")return t;let e=new URL(\"https://example.com\");return e.username=t,e.username}h(ke,\"le\");function xe(t){if(t===\"\")return t;let e=new URL(\"https://example.com\");return e.password=t,e.password}h(xe,\"fe\");function Zt(t){if(t===\"\")return t;if(/[\\t\\n\\r #%/:<>?@[\\]^\\\\|]/g.test(t))throw new TypeError(`Invalid hostname '${t}'`);let e=new URL(\"https://example.com\");return e.hostname=t,e.hostname}h(Zt,\"z\");function Qt(t){if(t===\"\")return t;if(/[^0-9a-fA-F[\\]:]/g.test(t))throw new TypeError(`Invalid IPv6 hostname '${t}'`);return t.toLowerCase()}h(Qt,\"j\");function qt(t){if(t===\"\"||/^[0-9]*$/.test(t)&&parseInt(t)<=65535)return t;throw new TypeError(`Invalid port '${t}'.`)}h(qt,\"K\");function $e(t){if(t===\"\")return t;let e=new URL(\"https://example.com\");return e.pathname=t[0]!==\"/\"?\"/-\"+t:t,t[0]!==\"/\"?e.pathname.substring(2,e.pathname.length):e.pathname}h($e,\"he\");function Ee(t){return t===\"\"?t:new URL(`data:${t}`).pathname}h(Ee,\"ue\");function Re(t){if(t===\"\")return t;let e=new URL(\"https://example.com\");return e.search=t,e.search.substring(1,e.search.length)}h(Re,\"de\");function Se(t){if(t===\"\")return t;let e=new URL(\"https://example.com\");return e.hash=t,e.hash.substring(1,e.hash.length)}h(Se,\"pe\");var st,k,y,d,Z,it,W,F,G,nt,m,g,$t,Yt,dt,Et,Q,rt,at,mt,E,C,wt,Rt,St,te,ht,gt,Wt,ee,Ct,se,ot,vt,K,q,N,X,Ot,ie,Pt,ne,Lt,re,At,ae,P,A,Tt,he,Ut,Fe=(Ut=class{constructor(t){p(this,m),p(this,$t),p(this,dt),p(this,Q),p(this,at),p(this,E),p(this,wt),p(this,St),p(this,ht),p(this,Wt),p(this,Ct),p(this,ot),p(this,K),p(this,N),p(this,Ot),p(this,Pt),p(this,Lt),p(this,At),p(this,P),p(this,Tt),p(this,st,void 0),p(this,k,[]),p(this,y,{}),p(this,d,0),p(this,Z,1),p(this,it,0),p(this,W,0),p(this,F,0),p(this,G,0),p(this,nt,!1),v(this,st,t)}get result(){return i(this,y)}parse(){for(v(this,k,Ft(i(this,st),!0));i(this,d)<i(this,k).length;v(this,d,i(this,d)+i(this,Z))){if(v(this,Z,1),i(this,k)[i(this,d)].type===\"END\"){if(i(this,W)===0){a(this,dt,Et).call(this),a(this,N,X).call(this)?a(this,m,g).call(this,9,1):a(this,K,q).call(this)?a(this,m,g).call(this,8,1):a(this,m,g).call(this,7,0);continue}else if(i(this,W)===2){a(this,Q,rt).call(this,5);continue}a(this,m,g).call(this,10,0);break}if(i(this,F)>0)if(a(this,Pt,ne).call(this))v(this,F,i(this,F)-1);else continue;if(a(this,Ot,ie).call(this)){v(this,F,i(this,F)+1);continue}switch(i(this,W)){case 0:a(this,wt,Rt).call(this)&&a(this,Q,rt).call(this,1);break;case 1:if(a(this,wt,Rt).call(this)){a(this,Tt,he).call(this);let t=7,e=1;a(this,St,te).call(this)?(t=2,e=3):i(this,nt)&&(t=2),a(this,m,g).call(this,t,e)}break;case 2:a(this,ht,gt).call(this)?a(this,Q,rt).call(this,3):(a(this,ot,vt).call(this)||a(this,K,q).call(this)||a(this,N,X).call(this))&&a(this,Q,rt).call(this,5);break;case 3:a(this,Wt,ee).call(this)?a(this,m,g).call(this,4,1):a(this,ht,gt).call(this)&&a(this,m,g).call(this,5,1);break;case 4:a(this,ht,gt).call(this)&&a(this,m,g).call(this,5,1);break;case 5:a(this,Lt,re).call(this)?v(this,G,i(this,G)+1):a(this,At,ae).call(this)&&v(this,G,i(this,G)-1),a(this,Ct,se).call(this)&&!i(this,G)?a(this,m,g).call(this,6,1):a(this,ot,vt).call(this)?a(this,m,g).call(this,7,0):a(this,K,q).call(this)?a(this,m,g).call(this,8,1):a(this,N,X).call(this)&&a(this,m,g).call(this,9,1);break;case 6:a(this,ot,vt).call(this)?a(this,m,g).call(this,7,0):a(this,K,q).call(this)?a(this,m,g).call(this,8,1):a(this,N,X).call(this)&&a(this,m,g).call(this,9,1);break;case 7:a(this,K,q).call(this)?a(this,m,g).call(this,8,1):a(this,N,X).call(this)&&a(this,m,g).call(this,9,1);break;case 8:a(this,N,X).call(this)&&a(this,m,g).call(this,9,1);break;case 9:break;case 10:break}}i(this,y).hostname!==void 0&&i(this,y).port===void 0&&(i(this,y).port=\"\")}},st=new WeakMap,k=new WeakMap,y=new WeakMap,d=new WeakMap,Z=new WeakMap,it=new WeakMap,W=new WeakMap,F=new WeakMap,G=new WeakMap,nt=new WeakMap,m=new WeakSet,g=h(function(t,e){switch(i(this,W)){case 0:break;case 1:i(this,y).protocol=a(this,P,A).call(this);break;case 2:break;case 3:i(this,y).username=a(this,P,A).call(this);break;case 4:i(this,y).password=a(this,P,A).call(this);break;case 5:i(this,y).hostname=a(this,P,A).call(this);break;case 6:i(this,y).port=a(this,P,A).call(this);break;case 7:i(this,y).pathname=a(this,P,A).call(this);break;case 8:i(this,y).search=a(this,P,A).call(this);break;case 9:i(this,y).hash=a(this,P,A).call(this);break;case 10:break}i(this,W)!==0&&t!==10&&([1,2,3,4].includes(i(this,W))&&[6,7,8,9].includes(t)&&(i(this,y).hostname??=\"\"),[1,2,3,4,5,6].includes(i(this,W))&&[8,9].includes(t)&&(i(this,y).pathname??=i(this,nt)?\"/\":\"\"),[1,2,3,4,5,6,7].includes(i(this,W))&&t===9&&(i(this,y).search??=\"\")),a(this,$t,Yt).call(this,t,e)},\"#r\"),$t=new WeakSet,Yt=h(function(t,e){v(this,W,t),v(this,it,i(this,d)+e),v(this,d,i(this,d)+e),v(this,Z,0)},\"#R\"),dt=new WeakSet,Et=h(function(){v(this,d,i(this,it)),v(this,Z,0)},\"#b\"),Q=new WeakSet,rt=h(function(t){a(this,dt,Et).call(this),v(this,W,t)},\"#u\"),at=new WeakSet,mt=h(function(t){return t<0&&(t=i(this,k).length-t),t<i(this,k).length?i(this,k)[t]:i(this,k)[i(this,k).length-1]},\"#m\"),E=new WeakSet,C=h(function(t,e){let s=a(this,at,mt).call(this,t);return s.value===e&&(s.type===\"CHAR\"||s.type===\"ESCAPED_CHAR\"||s.type===\"INVALID_CHAR\")},\"#a\"),wt=new WeakSet,Rt=h(function(){return a(this,E,C).call(this,i(this,d),\":\")},\"#P\"),St=new WeakSet,te=h(function(){return a(this,E,C).call(this,i(this,d)+1,\"/\")&&a(this,E,C).call(this,i(this,d)+2,\"/\")},\"#E\"),ht=new WeakSet,gt=h(function(){return a(this,E,C).call(this,i(this,d),\"@\")},\"#S\"),Wt=new WeakSet,ee=h(function(){return a(this,E,C).call(this,i(this,d),\":\")},\"#O\"),Ct=new WeakSet,se=h(function(){return a(this,E,C).call(this,i(this,d),\":\")},\"#k\"),ot=new WeakSet,vt=h(function(){return a(this,E,C).call(this,i(this,d),\"/\")},\"#x\"),K=new WeakSet,q=h(function(){if(a(this,E,C).call(this,i(this,d),\"?\"))return!0;if(i(this,k)[i(this,d)].value!==\"?\")return!1;let t=a(this,at,mt).call(this,i(this,d)-1);return t.type!==\"NAME\"&&t.type!==\"REGEX\"&&t.type!==\"CLOSE\"&&t.type!==\"ASTERISK\"},\"#h\"),N=new WeakSet,X=h(function(){return a(this,E,C).call(this,i(this,d),\"#\")},\"#f\"),Ot=new WeakSet,ie=h(function(){return i(this,k)[i(this,d)].type==\"OPEN\"},\"#T\"),Pt=new WeakSet,ne=h(function(){return i(this,k)[i(this,d)].type==\"CLOSE\"},\"#A\"),Lt=new WeakSet,re=h(function(){return a(this,E,C).call(this,i(this,d),\"[\")},\"#y\"),At=new WeakSet,ae=h(function(){return a(this,E,C).call(this,i(this,d),\"]\")},\"#w\"),P=new WeakSet,A=h(function(){let t=i(this,k)[i(this,d)],e=a(this,at,mt).call(this,i(this,it)).index;return i(this,st).substring(e,t.index)},\"#c\"),Tt=new WeakSet,he=h(function(){let t={};Object.assign(t,H),t.encodePart=_t;let e=ce(a(this,P,A).call(this),void 0,t);v(this,nt,zt(e))},\"#C\"),h(Ut,\"H\"),Ut),Mt=[\"protocol\",\"username\",\"password\",\"hostname\",\"port\",\"pathname\",\"search\",\"hash\"],j=\"*\";function jt(t,e){if(typeof t!=\"string\")throw new TypeError(\"parameter 1 is not of type 'string'.\");let s=new URL(t,e);return{protocol:s.protocol.substring(0,s.protocol.length-1),username:s.username,password:s.password,hostname:s.hostname,port:s.port,pathname:s.pathname,search:s.search!==\"\"?s.search.substring(1,s.search.length):void 0,hash:s.hash!==\"\"?s.hash.substring(1,s.hash.length):void 0}}h(jt,\"ge\");function U(t,e){return e?tt(t):t}h(U,\"b\");function Y(t,e,s){let r;if(typeof e.baseURL==\"string\")try{r=new URL(e.baseURL),e.protocol===void 0&&(t.protocol=U(r.protocol.substring(0,r.protocol.length-1),s)),!s&&e.protocol===void 0&&e.hostname===void 0&&e.port===void 0&&e.username===void 0&&(t.username=U(r.username,s)),!s&&e.protocol===void 0&&e.hostname===void 0&&e.port===void 0&&e.username===void 0&&e.password===void 0&&(t.password=U(r.password,s)),e.protocol===void 0&&e.hostname===void 0&&(t.hostname=U(r.hostname,s)),e.protocol===void 0&&e.hostname===void 0&&e.port===void 0&&(t.port=U(r.port,s)),e.protocol===void 0&&e.hostname===void 0&&e.port===void 0&&e.pathname===void 0&&(t.pathname=U(r.pathname,s)),e.protocol===void 0&&e.hostname===void 0&&e.port===void 0&&e.pathname===void 0&&e.search===void 0&&(t.search=U(r.search.substring(1,r.search.length),s)),e.protocol===void 0&&e.hostname===void 0&&e.port===void 0&&e.pathname===void 0&&e.search===void 0&&e.hash===void 0&&(t.hash=U(r.hash.substring(1,r.hash.length),s))}catch{throw new TypeError(`invalid baseURL '${e.baseURL}'.`)}if(typeof e.protocol==\"string\"&&(t.protocol=be(e.protocol,s)),typeof e.username==\"string\"&&(t.username=ve(e.username,s)),typeof e.password==\"string\"&&(t.password=ge(e.password,s)),typeof e.hostname==\"string\"&&(t.hostname=we(e.hostname,s)),typeof e.port==\"string\"&&(t.port=_e(e.port,t.protocol,s)),typeof e.pathname==\"string\"){if(t.pathname=e.pathname,r&&!ue(t.pathname,s)){let c=r.pathname.lastIndexOf(\"/\");c>=0&&(t.pathname=U(r.pathname.substring(0,c+1),s)+t.pathname)}t.pathname=ye(t.pathname,t.protocol,s)}return typeof e.search==\"string\"&&(t.search=me(e.search,s)),typeof e.hash==\"string\"&&(t.hash=de(e.hash,s)),t}h(Y,\"w\");function tt(t){return t.replace(/([+*?:{}()\\\\])/g,\"\\\\$1\")}h(tt,\"C\");function We(t){return t.replace(/([.+*?^${}()[\\]|/\\\\])/g,\"\\\\$1\")}h(We,\"Oe\");function Ce(t,e){e.delimiter??=\"/#?\",e.prefixes??=\"./\",e.sensitive??=!1,e.strict??=!1,e.end??=!0,e.start??=!0,e.endsWith=\"\";let s=\".*\",r=`[^${We(e.delimiter)}]+?`,c=/[$_\\u200C\\u200D\\p{ID_Continue}]/u,f=\"\";for(let l=0;l<t.length;++l){let n=t[l];if(n.type===3){if(n.modifier===3){f+=tt(n.value);continue}f+=`{${tt(n.value)}}${V(n.modifier)}`;continue}let u=n.hasCustomName(),o=!!n.suffix.length||!!n.prefix.length&&(n.prefix.length!==1||!e.prefixes.includes(n.prefix)),x=l>0?t[l-1]:null,L=l<t.length-1?t[l+1]:null;if(!o&&u&&n.type===1&&n.modifier===3&&L&&!L.prefix.length&&!L.suffix.length)if(L.type===3){let z=L.value.length>0?L.value[0]:\"\";o=c.test(z)}else o=!L.hasCustomName();if(!o&&!n.prefix.length&&x&&x.type===3){let z=x.value[x.value.length-1];o=e.prefixes.includes(z)}o&&(f+=\"{\"),f+=tt(n.prefix),u&&(f+=`:${n.name}`),n.type===2?f+=`(${n.value})`:n.type===1?u||(f+=`(${r})`):n.type===0&&(!u&&(!x||x.type===3||x.modifier!==3||o||n.prefix!==\"\")?f+=\"*\":f+=`(${s})`),n.type===1&&u&&n.suffix.length&&c.test(n.suffix[0])&&(f+=\"\\\\\"),f+=tt(n.suffix),o&&(f+=\"}\"),n.modifier!==3&&(f+=V(n.modifier))}return f}h(Ce,\"ke\");var M,B,lt,_,T,ct,It,Oe=(It=class{constructor(t={},e,s){p(this,M,void 0),p(this,B,{}),p(this,lt,{}),p(this,_,{}),p(this,T,{}),p(this,ct,!1);try{let r;if(typeof e==\"string\"?r=e:s=e,typeof t==\"string\"){let n=new Fe(t);if(n.parse(),t=n.result,r===void 0&&typeof t.protocol!=\"string\")throw new TypeError(\"A base URL must be provided for a relative constructor string.\");t.baseURL=r}else{if(!t||typeof t!=\"object\")throw new TypeError(\"parameter 1 is not of type 'string' and cannot convert to dictionary.\");if(r)throw new TypeError(\"parameter 1 is not of type 'string'.\")}typeof s>\"u\"&&(s={ignoreCase:!1});let c={ignoreCase:s.ignoreCase===!0};v(this,M,Y({pathname:j,protocol:j,username:j,password:j,hostname:j,port:j,search:j,hash:j},t,!0)),Jt(i(this,M).protocol)===i(this,M).port&&(i(this,M).port=\"\");let l;for(l of Mt){if(!(l in i(this,M)))continue;let n={},u=i(this,M)[l];switch(i(this,lt)[l]=[],l){case\"protocol\":Object.assign(n,H),n.encodePart=_t;break;case\"username\":Object.assign(n,H),n.encodePart=ke;break;case\"password\":Object.assign(n,H),n.encodePart=xe;break;case\"hostname\":Object.assign(n,je),Vt(u)?n.encodePart=Qt:n.encodePart=Zt;break;case\"port\":Object.assign(n,H),n.encodePart=qt;break;case\"pathname\":zt(i(this,B).protocol)?(Object.assign(n,He,c),n.encodePart=$e):(Object.assign(n,H,c),n.encodePart=Ee);break;case\"search\":Object.assign(n,H,c),n.encodePart=Re;break;case\"hash\":Object.assign(n,H,c),n.encodePart=Se;break}try{i(this,T)[l]=Gt(u,n),i(this,B)[l]=Kt(i(this,T)[l],i(this,lt)[l],n),i(this,_)[l]=Ce(i(this,T)[l],n),v(this,ct,i(this,ct)||i(this,T)[l].some(o=>o.type===2))}catch{throw new TypeError(`invalid ${l} pattern '${i(this,M)[l]}'.`)}}}catch(r){throw new TypeError(`Failed to construct 'URLPattern': ${r.message}`)}}test(t={},e){let s={pathname:\"\",protocol:\"\",username:\"\",password:\"\",hostname:\"\",port:\"\",search:\"\",hash:\"\"};if(typeof t!=\"string\"&&e)throw new TypeError(\"parameter 1 is not of type 'string'.\");if(typeof t>\"u\")return!1;try{typeof t==\"object\"?s=Y(s,t,!1):s=Y(s,jt(t,e),!1)}catch{return!1}let r;for(r of Mt)if(!i(this,B)[r].exec(s[r]))return!1;return!0}exec(t={},e){let s={pathname:\"\",protocol:\"\",username:\"\",password:\"\",hostname:\"\",port:\"\",search:\"\",hash:\"\"};if(typeof t!=\"string\"&&e)throw new TypeError(\"parameter 1 is not of type 'string'.\");if(typeof t>\"u\")return;try{typeof t==\"object\"?s=Y(s,t,!1):s=Y(s,jt(t,e),!1)}catch{return null}let r={};e?r.inputs=[t,e]:r.inputs=[t];let c;for(c of Mt){let f=i(this,B)[c].exec(s[c]);if(!f)return null;let l={};for(let[n,u]of i(this,lt)[c].entries())if(typeof u==\"string\"||typeof u==\"number\"){let o=f[n+1];l[u]=o}r[c]={input:s[c]??\"\",groups:l}}return r}static compareComponent(t,e,s){let r=h((n,u)=>{for(let o of[\"type\",\"modifier\",\"prefix\",\"value\",\"suffix\"]){if(n[o]<u[o])return-1;if(n[o]!==u[o])return 1}return 0},\"o\"),c=new ut(3,\"\",\"\",\"\",\"\",3),f=new ut(0,\"\",\"\",\"\",\"\",3),l=h((n,u)=>{let o=0;for(;o<Math.min(n.length,u.length);++o){let x=r(n[o],u[o]);if(x)return x}return n.length===u.length?0:r(n[o]??c,u[o]??c)},\"s\");return!i(e,_)[t]&&!i(s,_)[t]?0:i(e,_)[t]&&!i(s,_)[t]?l(i(e,T)[t],[f]):!i(e,_)[t]&&i(s,_)[t]?l([f],i(s,T)[t]):l(i(e,T)[t],i(s,T)[t])}get protocol(){return i(this,_).protocol}get username(){return i(this,_).username}get password(){return i(this,_).password}get hostname(){return i(this,_).hostname}get port(){return i(this,_).port}get pathname(){return i(this,_).pathname}get search(){return i(this,_).search}get hash(){return i(this,_).hash}get hasRegExpGroups(){return i(this,ct)}},M=new WeakMap,B=new WeakMap,lt=new WeakMap,_=new WeakMap,T=new WeakMap,ct=new WeakMap,h(It,\"me\"),It);globalThis.URLPattern||(globalThis.URLPattern=Oe);\n"