{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/T_02/cursor-clone/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport Image from \"next/image\";\nimport { useState } from \"react\";\n\n// Navigation Component\nfunction Navigation() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  return (\n    <nav className=\"fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <div className=\"w-8 h-8 bg-black rounded-md flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">C</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Navigation Links - Desktop */}\n          <div className=\"hidden md:block\">\n            <div className=\"ml-10 flex items-baseline space-x-8\">\n              <a href=\"/pricing\" className=\"text-gray-700 hover:text-black px-3 py-2 text-sm font-medium transition-colors\">\n                定价\n              </a>\n              <a href=\"/features\" className=\"text-gray-700 hover:text-black px-3 py-2 text-sm font-medium transition-colors\">\n                功能\n              </a>\n              <a href=\"/enterprise\" className=\"text-gray-700 hover:text-black px-3 py-2 text-sm font-medium transition-colors\">\n                企业\n              </a>\n              <a href=\"/blog\" className=\"text-gray-700 hover:text-black px-3 py-2 text-sm font-medium transition-colors\">\n                博客\n              </a>\n              <a href=\"https://forum.cursor.com\" className=\"text-gray-700 hover:text-black px-3 py-2 text-sm font-medium transition-colors\">\n                论坛\n              </a>\n              <a href=\"https://anysphere.inc/\" className=\"text-gray-700 hover:text-black px-3 py-2 text-sm font-medium transition-colors\">\n                招聘\n              </a>\n            </div>\n          </div>\n\n          {/* Right side buttons - Desktop */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <a href=\"/api/auth/login\" className=\"text-gray-700 hover:text-black px-3 py-2 text-sm font-medium transition-colors\">\n              登录\n            </a>\n            <a\n              href=\"/downloads\"\n              className=\"bg-black text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-800 transition-colors\"\n            >\n              下载\n            </a>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"text-gray-700 hover:text-black p-2\"\n            >\n              <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                {isMenuOpen ? (\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                ) : (\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                )}\n              </svg>\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile menu */}\n        {isMenuOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1 bg-white border-t border-gray-200\">\n              <a href=\"/pricing\" className=\"block px-3 py-2 text-base font-medium text-gray-700 hover:text-black\">\n                定价\n              </a>\n              <a href=\"/features\" className=\"block px-3 py-2 text-base font-medium text-gray-700 hover:text-black\">\n                功能\n              </a>\n              <a href=\"/enterprise\" className=\"block px-3 py-2 text-base font-medium text-gray-700 hover:text-black\">\n                企业\n              </a>\n              <a href=\"/blog\" className=\"block px-3 py-2 text-base font-medium text-gray-700 hover:text-black\">\n                博客\n              </a>\n              <a href=\"https://forum.cursor.com\" className=\"block px-3 py-2 text-base font-medium text-gray-700 hover:text-black\">\n                论坛\n              </a>\n              <a href=\"https://anysphere.inc/\" className=\"block px-3 py-2 text-base font-medium text-gray-700 hover:text-black\">\n                招聘\n              </a>\n              <div className=\"border-t border-gray-200 pt-4\">\n                <a href=\"/api/auth/login\" className=\"block px-3 py-2 text-base font-medium text-gray-700 hover:text-black\">\n                  登录\n                </a>\n                <a href=\"/downloads\" className=\"block mx-3 mt-2 bg-black text-white px-4 py-2 rounded-md text-base font-medium text-center\">\n                  下载\n                </a>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </nav>\n  );\n}\n\n// Hero Section Component\nfunction HeroSection() {\n  return (\n    <section className=\"pt-32 pb-20 px-4 sm:px-6 lg:px-8 animate-fade-in-up\">\n      <div className=\"max-w-7xl mx-auto\">\n        <div className=\"text-center\">\n          {/* Hero Background Image */}\n          <div className=\"relative mb-12\">\n            <div className=\"absolute inset-0 bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 rounded-3xl opacity-20 blur-3xl animate-pulse\"></div>\n            <div className=\"relative bg-gradient-to-br from-gray-50 to-white rounded-3xl p-8 md:p-16 shadow-xl border border-gray-100\">\n              <h1 className=\"text-4xl md:text-6xl lg:text-7xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-6\">\n                AI 代码编辑器\n              </h1>\n              <p className=\"text-xl md:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed\">\n                旨在让你获得超凡的生产力， Cursor 是使用 AI 编写代码的最佳方式。\n              </p>\n\n              {/* Download Buttons */}\n              <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center mb-12\">\n                <button className=\"bg-black text-white px-8 py-4 rounded-lg text-lg font-medium hover:bg-gray-800 transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl\">\n                  下载 MacOS\n                </button>\n                <a href=\"/downloads\" className=\"text-gray-600 hover:text-black text-lg underline hover:no-underline transition-all duration-300\">\n                  所有下载\n                </a>\n              </div>\n\n              {/* Hero Image Placeholder */}\n              <div className=\"relative\">\n                <div className=\"bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl h-64 md:h-96 flex items-center justify-center shadow-inner border border-gray-200\">\n                  <div className=\"text-center\">\n                    <div className=\"w-16 h-16 bg-black rounded-lg flex items-center justify-center mx-auto mb-4\">\n                      <span className=\"text-white font-bold text-xl\">C</span>\n                    </div>\n                    <span className=\"text-gray-500 text-lg\">Cursor 编辑器界面预览</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n\n// Company Trust Section\nfunction CompanyTrustSection() {\n  const companies = [\n    \"Johnson & Johnson\", \"OpenAI\", \"Stripe\", \"Samsung\", \"Instacart\",\n    \"Perplexity\", \"Ramp\", \"Shopify\", \"US Foods\", \"MercadoLibre\"\n  ];\n\n  return (\n    <section className=\"py-16 px-4 sm:px-6 lg:px-8 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-8\">由工程师信任</h2>\n          <div className=\"grid grid-cols-2 md:grid-cols-5 gap-8 items-center\">\n            {companies.map((company, index) => (\n              <div key={index} className=\"text-gray-600 font-medium text-sm md:text-base\">\n                {company}\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n\n// Features Section\nfunction FeaturesSection() {\n  return (\n    <section className=\"py-20 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-7xl mx-auto\">\n        {/* Feature 1 */}\n        <div className=\"mb-20\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              Tab，Tab，再来一次 Tab\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              Cursor 会根据上下文预测你的下一步编辑，让你轻松完成修改。\n            </p>\n          </div>\n          <div className=\"bg-gray-100 rounded-xl h-64 md:h-96 flex items-center justify-center\">\n            <span className=\"text-gray-500 text-lg\">Tab 补全功能演示</span>\n          </div>\n        </div>\n\n        {/* Feature 2 */}\n        <div className=\"mb-20\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              熟知你的代码库\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              从你的代码库或文档中获取答案，还能引用具体文件。 一键即可应用模型生成的代码。\n            </p>\n          </div>\n          <div className=\"bg-gray-100 rounded-xl h-64 md:h-96 flex items-center justify-center\">\n            <span className=\"text-gray-500 text-lg\">代码库理解功能演示</span>\n          </div>\n        </div>\n\n        {/* Feature 3 */}\n        <div className=\"mb-20\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              用自然语言编辑\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              借助 Cursor，你可以通过简单指令来编写或修改代码。 仅需一个提示，就能更新整段类或函数。\n            </p>\n          </div>\n          <div className=\"bg-gray-100 rounded-xl h-64 md:h-96 flex items-center justify-center\">\n            <span className=\"text-gray-500 text-lg\">自然语言编辑功能演示</span>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n\n// Product Features Cards Section\nfunction ProductFeaturesSection() {\n  const features = [\n    {\n      title: \"前沿智能\",\n      description: \"由定制化模型与前沿模型混合驱动，Cursor 既聪明又快速。\"\n    },\n    {\n      title: \"熟悉的体验\",\n      description: \"一键即可导入所有扩展、主题和快捷键绑定。\"\n    },\n    {\n      title: \"隐私选项\",\n      description: \"启用隐私模式后，你的代码不会储存于远程。Cursor 已通过 SOC 2 认证。\"\n    }\n  ];\n\n  return (\n    <section className=\"py-20 px-4 sm:px-6 lg:px-8 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n            更快构建软件\n          </h2>\n          <p className=\"text-xl text-gray-600 mb-8\">\n            智能、高速且熟悉，Cursor 是 AI 写代码的最佳选择。\n          </p>\n          <a href=\"/features\" className=\"text-blue-600 hover:text-blue-800 font-medium\">\n            更多功能\n          </a>\n        </div>\n\n        <div className=\"grid md:grid-cols-3 gap-8\">\n          {features.map((feature, index) => (\n            <div key={index} className=\"bg-white p-8 rounded-xl shadow-sm border border-gray-200\">\n              <h3 className=\"text-xl font-bold text-gray-900 mb-4\">{feature.title}</h3>\n              <p className=\"text-gray-600\">{feature.description}</p>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n}\n\n// Testimonials Section\nfunction TestimonialsSection() {\n  const testimonials = [\n    {\n      quote: \"Cursor 至少比 Copilot 提升两倍效率。 有了 AI 搭档编程非常棒，对我和团队而言都是极大的加速器。\",\n      author: \"Ben Bernard\",\n      company: \"Instacart\"\n    },\n    {\n      quote: \"写代码时，Cursor 的 Tab 补全有时就像魔法一样——大约四分之一的情况能完美猜中我想做什么。\",\n      author: \"Kevin Whinnery\",\n      company: \"OpenAI\"\n    },\n    {\n      quote: \"对我而言，Cursor 是近几年里工作流程提升最大的工具。\",\n      author: \"Sawyer Hood\",\n      company: \"Figma\"\n    }\n  ];\n\n  return (\n    <section className=\"py-20 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-7xl mx-auto\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n            深受全球顶尖开发者喜爱\n          </h2>\n          <p className=\"text-xl text-gray-600\">\n            世界各地的工程师都主动选择使用 Cursor。\n          </p>\n        </div>\n\n        <div className=\"grid md:grid-cols-3 gap-8\">\n          {testimonials.map((testimonial, index) => (\n            <div key={index} className=\"bg-gray-50 p-8 rounded-xl\">\n              <p className=\"text-gray-700 mb-6 italic\">\"{testimonial.quote}\"</p>\n              <div className=\"flex items-center\">\n                <div className=\"w-12 h-12 bg-gray-300 rounded-full mr-4\"></div>\n                <div>\n                  <div className=\"font-bold text-gray-900\">{testimonial.author}</div>\n                  <div className=\"text-gray-600\">{testimonial.company}</div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n}\n\n// Footer Component\nfunction Footer() {\n  return (\n    <footer className=\"bg-black text-white py-16 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-7xl mx-auto\">\n        {/* Call to Action */}\n        <div className=\"text-center mb-16\">\n          <div className=\"mb-8\">\n            <div className=\"w-16 h-16 bg-white rounded-lg flex items-center justify-center mx-auto mb-6\">\n              <span className=\"text-black font-bold text-xl\">C</span>\n            </div>\n          </div>\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-4\">立即试用 Cursor</h2>\n          <p className=\"text-xl text-gray-300 mb-8\">免费下载</p>\n          <div className=\"mb-8\">\n            <a href=\"mailto:<EMAIL>\" className=\"text-gray-300 hover:text-white\">\n              <EMAIL>\n            </a>\n          </div>\n\n          {/* Social Links */}\n          <div className=\"flex justify-center space-x-6 mb-12\">\n            <a href=\"https://x.com/cursor_ai\" className=\"text-gray-300 hover:text-white\">\n              Twitter\n            </a>\n            <a href=\"https://github.com/getcursor/cursor\" className=\"text-gray-300 hover:text-white\">\n              GitHub\n            </a>\n            <a href=\"https://www.reddit.com/r/cursor\" className=\"text-gray-300 hover:text-white\">\n              Reddit\n            </a>\n            <a href=\"https://www.youtube.com/@cursor_ai\" className=\"text-gray-300 hover:text-white\">\n              YouTube\n            </a>\n          </div>\n        </div>\n\n        {/* Footer Links */}\n        <div className=\"grid md:grid-cols-4 gap-8 mb-12\">\n          <div>\n            <h3 className=\"font-bold mb-4\">Product</h3>\n            <ul className=\"space-y-2\">\n              <li><a href=\"/pricing\" className=\"text-gray-300 hover:text-white\">定价</a></li>\n              <li><a href=\"/features\" className=\"text-gray-300 hover:text-white\">Features</a></li>\n              <li><a href=\"/enterprise\" className=\"text-gray-300 hover:text-white\">Enterprise</a></li>\n              <li><a href=\"/downloads\" className=\"text-gray-300 hover:text-white\">下载</a></li>\n              <li><a href=\"/students\" className=\"text-gray-300 hover:text-white\">Students</a></li>\n            </ul>\n          </div>\n\n          <div>\n            <h3 className=\"font-bold mb-4\">Resources</h3>\n            <ul className=\"space-y-2\">\n              <li><a href=\"https://docs.cursor.com\" className=\"text-gray-300 hover:text-white\">文档</a></li>\n              <li><a href=\"/blog\" className=\"text-gray-300 hover:text-white\">Blog</a></li>\n              <li><a href=\"https://forum.cursor.com\" className=\"text-gray-300 hover:text-white\">论坛</a></li>\n              <li><a href=\"/changelog\" className=\"text-gray-300 hover:text-white\">更新日志</a></li>\n            </ul>\n          </div>\n\n          <div>\n            <h3 className=\"font-bold mb-4\">Company</h3>\n            <ul className=\"space-y-2\">\n              <li><a href=\"https://anysphere.inc\" className=\"text-gray-300 hover:text-white\">公司</a></li>\n              <li><a href=\"https://anysphere.inc\" className=\"text-gray-300 hover:text-white\">招聘</a></li>\n              <li><a href=\"/community\" className=\"text-gray-300 hover:text-white\">社区</a></li>\n            </ul>\n          </div>\n\n          <div>\n            <h3 className=\"font-bold mb-4\">Legal</h3>\n            <ul className=\"space-y-2\">\n              <li><a href=\"/terms-of-service\" className=\"text-gray-300 hover:text-white\">条款</a></li>\n              <li><a href=\"/security\" className=\"text-gray-300 hover:text-white\">安全</a></li>\n              <li><a href=\"/privacy\" className=\"text-gray-300 hover:text-white\">隐私</a></li>\n            </ul>\n          </div>\n        </div>\n\n        {/* Bottom */}\n        <div className=\"border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center\">\n          <div className=\"text-gray-300 mb-4 md:mb-0\">\n            © 2025 Made by <a href=\"https://anysphere.inc\" className=\"hover:text-white\">Anysphere</a>\n          </div>\n          <div className=\"flex items-center space-x-4\">\n            <span className=\"text-gray-300\">Chinese</span>\n            <span className=\"text-gray-300\">SOC 2 Certified</span>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-white\">\n      <Navigation />\n      <HeroSection />\n      <CompanyTrustSection />\n      <FeaturesSection />\n      <ProductFeaturesSection />\n      <TestimonialsSection />\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;;;AAHA;;AAKA,uBAAuB;AACvB,SAAS;;IACP,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;;;;;;;;;;;sCAMrD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,MAAK;wCAAW,WAAU;kDAAiF;;;;;;kDAG9G,6LAAC;wCAAE,MAAK;wCAAY,WAAU;kDAAiF;;;;;;kDAG/G,6LAAC;wCAAE,MAAK;wCAAc,WAAU;kDAAiF;;;;;;kDAGjH,6LAAC;wCAAE,MAAK;wCAAQ,WAAU;kDAAiF;;;;;;kDAG3G,6LAAC;wCAAE,MAAK;wCAA2B,WAAU;kDAAiF;;;;;;kDAG9H,6LAAC;wCAAE,MAAK;wCAAyB,WAAU;kDAAiF;;;;;;;;;;;;;;;;;sCAOhI,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,MAAK;oCAAkB,WAAU;8CAAiF;;;;;;8CAGrH,6LAAC;oCACC,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS,IAAM,cAAc,CAAC;gCAC9B,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CAC7D,2BACC,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;6DAErE,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAQ9E,4BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,MAAK;gCAAW,WAAU;0CAAuE;;;;;;0CAGpG,6LAAC;gCAAE,MAAK;gCAAY,WAAU;0CAAuE;;;;;;0CAGrG,6LAAC;gCAAE,MAAK;gCAAc,WAAU;0CAAuE;;;;;;0CAGvG,6LAAC;gCAAE,MAAK;gCAAQ,WAAU;0CAAuE;;;;;;0CAGjG,6LAAC;gCAAE,MAAK;gCAA2B,WAAU;0CAAuE;;;;;;0CAGpH,6LAAC;gCAAE,MAAK;gCAAyB,WAAU;0CAAuE;;;;;;0CAGlH,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,MAAK;wCAAkB,WAAU;kDAAuE;;;;;;kDAG3G,6LAAC;wCAAE,MAAK;wCAAa,WAAU;kDAA6F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU5I;GA1GS;KAAA;AA4GT,yBAAyB;AACzB,SAAS;IACP,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BAEb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA2H;;;;;;8CAGzI,6LAAC;oCAAE,WAAU;8CAA2E;;;;;;8CAKxF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAO,WAAU;sDAAuJ;;;;;;sDAGzK,6LAAC;4CAAE,MAAK;4CAAa,WAAU;sDAAkG;;;;;;;;;;;;8CAMnI,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAA+B;;;;;;;;;;;8DAEjD,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU5D;MA3CS;AA6CT,wBAAwB;AACxB,SAAS;IACP,MAAM,YAAY;QAChB;QAAqB;QAAU;QAAU;QAAW;QACpD;QAAc;QAAQ;QAAW;QAAY;KAC9C;IAED,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAI,WAAU;kCACZ,UAAU,GAAG,CAAC,CAAC,SAAS,sBACvB,6LAAC;gCAAgB,WAAU;0CACxB;+BADO;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB;MAtBS;AAwBT,mBAAmB;AACnB,SAAS;IACP,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,6LAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAIzD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,WAAU;0CAAwB;;;;;;;;;;;;;;;;;8BAK5C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,6LAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAIzD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,WAAU;0CAAwB;;;;;;;;;;;;;;;;;8BAK5C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,6LAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAIzD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMpD;MAnDS;AAqDT,iCAAiC;AACjC,SAAS;IACP,MAAM,WAAW;QACf;YACE,OAAO;YACP,aAAa;QACf;QACA;YACE,OAAO;YACP,aAAa;QACf;QACA;YACE,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,6LAAC;4BAAE,MAAK;4BAAY,WAAU;sCAAgD;;;;;;;;;;;;8BAKhF,6LAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;4BAAgB,WAAU;;8CACzB,6LAAC;oCAAG,WAAU;8CAAwC,QAAQ,KAAK;;;;;;8CACnE,6LAAC;oCAAE,WAAU;8CAAiB,QAAQ,WAAW;;;;;;;2BAFzC;;;;;;;;;;;;;;;;;;;;;AAStB;MA1CS;AA4CT,uBAAuB;AACvB,SAAS;IACP,MAAM,eAAe;QACnB;YACE,OAAO;YACP,QAAQ;YACR,SAAS;QACX;QACA;YACE,OAAO;YACP,QAAQ;YACR,SAAS;QACX;QACA;YACE,OAAO;YACP,QAAQ;YACR,SAAS;QACX;KACD;IAED,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAKvC,6LAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,6LAAC;4BAAgB,WAAU;;8CACzB,6LAAC;oCAAE,WAAU;;wCAA4B;wCAAE,YAAY,KAAK;wCAAC;;;;;;;8CAC7D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DAA2B,YAAY,MAAM;;;;;;8DAC5D,6LAAC;oDAAI,WAAU;8DAAiB,YAAY,OAAO;;;;;;;;;;;;;;;;;;;2BAN/C;;;;;;;;;;;;;;;;;;;;;AAetB;MAhDS;AAkDT,mBAAmB;AACnB,SAAS;IACP,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAA+B;;;;;;;;;;;;;;;;sCAGnD,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;sCACpD,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAC1C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,MAAK;gCAAuB,WAAU;0CAAiC;;;;;;;;;;;sCAM5E,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,MAAK;oCAA0B,WAAU;8CAAiC;;;;;;8CAG7E,6LAAC;oCAAE,MAAK;oCAAsC,WAAU;8CAAiC;;;;;;8CAGzF,6LAAC;oCAAE,MAAK;oCAAkC,WAAU;8CAAiC;;;;;;8CAGrF,6LAAC;oCAAE,MAAK;oCAAqC,WAAU;8CAAiC;;;;;;;;;;;;;;;;;;8BAO5F,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAiB;;;;;;8CAC/B,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG,cAAA,6LAAC;gDAAE,MAAK;gDAAW,WAAU;0DAAiC;;;;;;;;;;;sDAClE,6LAAC;sDAAG,cAAA,6LAAC;gDAAE,MAAK;gDAAY,WAAU;0DAAiC;;;;;;;;;;;sDACnE,6LAAC;sDAAG,cAAA,6LAAC;gDAAE,MAAK;gDAAc,WAAU;0DAAiC;;;;;;;;;;;sDACrE,6LAAC;sDAAG,cAAA,6LAAC;gDAAE,MAAK;gDAAa,WAAU;0DAAiC;;;;;;;;;;;sDACpE,6LAAC;sDAAG,cAAA,6LAAC;gDAAE,MAAK;gDAAY,WAAU;0DAAiC;;;;;;;;;;;;;;;;;;;;;;;sCAIvE,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAiB;;;;;;8CAC/B,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG,cAAA,6LAAC;gDAAE,MAAK;gDAA0B,WAAU;0DAAiC;;;;;;;;;;;sDACjF,6LAAC;sDAAG,cAAA,6LAAC;gDAAE,MAAK;gDAAQ,WAAU;0DAAiC;;;;;;;;;;;sDAC/D,6LAAC;sDAAG,cAAA,6LAAC;gDAAE,MAAK;gDAA2B,WAAU;0DAAiC;;;;;;;;;;;sDAClF,6LAAC;sDAAG,cAAA,6LAAC;gDAAE,MAAK;gDAAa,WAAU;0DAAiC;;;;;;;;;;;;;;;;;;;;;;;sCAIxE,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAiB;;;;;;8CAC/B,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG,cAAA,6LAAC;gDAAE,MAAK;gDAAwB,WAAU;0DAAiC;;;;;;;;;;;sDAC/E,6LAAC;sDAAG,cAAA,6LAAC;gDAAE,MAAK;gDAAwB,WAAU;0DAAiC;;;;;;;;;;;sDAC/E,6LAAC;sDAAG,cAAA,6LAAC;gDAAE,MAAK;gDAAa,WAAU;0DAAiC;;;;;;;;;;;;;;;;;;;;;;;sCAIxE,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAiB;;;;;;8CAC/B,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG,cAAA,6LAAC;gDAAE,MAAK;gDAAoB,WAAU;0DAAiC;;;;;;;;;;;sDAC3E,6LAAC;sDAAG,cAAA,6LAAC;gDAAE,MAAK;gDAAY,WAAU;0DAAiC;;;;;;;;;;;sDACnE,6LAAC;sDAAG,cAAA,6LAAC;gDAAE,MAAK;gDAAW,WAAU;0DAAiC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAMxE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;gCAA6B;8CAC3B,6LAAC;oCAAE,MAAK;oCAAwB,WAAU;8CAAmB;;;;;;;;;;;;sCAE9E,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5C;MA3FS;AA6FM,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;;;;;0BACD,6LAAC;;;;;0BACD,6LAAC;;;;;0BACD,6LAAC;;;;;0BACD,6LAAC;;;;;0BACD,6LAAC;;;;;0BACD,6LAAC;;;;;;;;;;;AAGP;MAZwB"}}, {"offset": {"line": 1486, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1496, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/T_02/cursor-clone/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE$2\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PORTAL_TYPE:\n          return \"Portal\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function disabledLog() {}\n    function disableLogs() {\n      if (0 === disabledDepth) {\n        prevLog = console.log;\n        prevInfo = console.info;\n        prevWarn = console.warn;\n        prevError = console.error;\n        prevGroup = console.group;\n        prevGroupCollapsed = console.groupCollapsed;\n        prevGroupEnd = console.groupEnd;\n        var props = {\n          configurable: !0,\n          enumerable: !0,\n          value: disabledLog,\n          writable: !0\n        };\n        Object.defineProperties(console, {\n          info: props,\n          log: props,\n          warn: props,\n          error: props,\n          group: props,\n          groupCollapsed: props,\n          groupEnd: props\n        });\n      }\n      disabledDepth++;\n    }\n    function reenableLogs() {\n      disabledDepth--;\n      if (0 === disabledDepth) {\n        var props = { configurable: !0, enumerable: !0, writable: !0 };\n        Object.defineProperties(console, {\n          log: assign({}, props, { value: prevLog }),\n          info: assign({}, props, { value: prevInfo }),\n          warn: assign({}, props, { value: prevWarn }),\n          error: assign({}, props, { value: prevError }),\n          group: assign({}, props, { value: prevGroup }),\n          groupCollapsed: assign({}, props, { value: prevGroupCollapsed }),\n          groupEnd: assign({}, props, { value: prevGroupEnd })\n        });\n      }\n      0 > disabledDepth &&\n        console.error(\n          \"disabledDepth fell below zero. This is a bug in React. Please file an issue.\"\n        );\n    }\n    function describeBuiltInComponentFrame(name) {\n      if (void 0 === prefix)\n        try {\n          throw Error();\n        } catch (x) {\n          var match = x.stack.trim().match(/\\n( *(at )?)/);\n          prefix = (match && match[1]) || \"\";\n          suffix =\n            -1 < x.stack.indexOf(\"\\n    at\")\n              ? \" (<anonymous>)\"\n              : -1 < x.stack.indexOf(\"@\")\n                ? \"@unknown:0:0\"\n                : \"\";\n        }\n      return \"\\n\" + prefix + name + suffix;\n    }\n    function describeNativeComponentFrame(fn, construct) {\n      if (!fn || reentry) return \"\";\n      var frame = componentFrameCache.get(fn);\n      if (void 0 !== frame) return frame;\n      reentry = !0;\n      frame = Error.prepareStackTrace;\n      Error.prepareStackTrace = void 0;\n      var previousDispatcher = null;\n      previousDispatcher = ReactSharedInternals.H;\n      ReactSharedInternals.H = null;\n      disableLogs();\n      try {\n        var RunInRootFrame = {\n          DetermineComponentFrameRoot: function () {\n            try {\n              if (construct) {\n                var Fake = function () {\n                  throw Error();\n                };\n                Object.defineProperty(Fake.prototype, \"props\", {\n                  set: function () {\n                    throw Error();\n                  }\n                });\n                if (\"object\" === typeof Reflect && Reflect.construct) {\n                  try {\n                    Reflect.construct(Fake, []);\n                  } catch (x) {\n                    var control = x;\n                  }\n                  Reflect.construct(fn, [], Fake);\n                } else {\n                  try {\n                    Fake.call();\n                  } catch (x$0) {\n                    control = x$0;\n                  }\n                  fn.call(Fake.prototype);\n                }\n              } else {\n                try {\n                  throw Error();\n                } catch (x$1) {\n                  control = x$1;\n                }\n                (Fake = fn()) &&\n                  \"function\" === typeof Fake.catch &&\n                  Fake.catch(function () {});\n              }\n            } catch (sample) {\n              if (sample && control && \"string\" === typeof sample.stack)\n                return [sample.stack, control.stack];\n            }\n            return [null, null];\n          }\n        };\n        RunInRootFrame.DetermineComponentFrameRoot.displayName =\n          \"DetermineComponentFrameRoot\";\n        var namePropDescriptor = Object.getOwnPropertyDescriptor(\n          RunInRootFrame.DetermineComponentFrameRoot,\n          \"name\"\n        );\n        namePropDescriptor &&\n          namePropDescriptor.configurable &&\n          Object.defineProperty(\n            RunInRootFrame.DetermineComponentFrameRoot,\n            \"name\",\n            { value: \"DetermineComponentFrameRoot\" }\n          );\n        var _RunInRootFrame$Deter =\n            RunInRootFrame.DetermineComponentFrameRoot(),\n          sampleStack = _RunInRootFrame$Deter[0],\n          controlStack = _RunInRootFrame$Deter[1];\n        if (sampleStack && controlStack) {\n          var sampleLines = sampleStack.split(\"\\n\"),\n            controlLines = controlStack.split(\"\\n\");\n          for (\n            _RunInRootFrame$Deter = namePropDescriptor = 0;\n            namePropDescriptor < sampleLines.length &&\n            !sampleLines[namePropDescriptor].includes(\n              \"DetermineComponentFrameRoot\"\n            );\n\n          )\n            namePropDescriptor++;\n          for (\n            ;\n            _RunInRootFrame$Deter < controlLines.length &&\n            !controlLines[_RunInRootFrame$Deter].includes(\n              \"DetermineComponentFrameRoot\"\n            );\n\n          )\n            _RunInRootFrame$Deter++;\n          if (\n            namePropDescriptor === sampleLines.length ||\n            _RunInRootFrame$Deter === controlLines.length\n          )\n            for (\n              namePropDescriptor = sampleLines.length - 1,\n                _RunInRootFrame$Deter = controlLines.length - 1;\n              1 <= namePropDescriptor &&\n              0 <= _RunInRootFrame$Deter &&\n              sampleLines[namePropDescriptor] !==\n                controlLines[_RunInRootFrame$Deter];\n\n            )\n              _RunInRootFrame$Deter--;\n          for (\n            ;\n            1 <= namePropDescriptor && 0 <= _RunInRootFrame$Deter;\n            namePropDescriptor--, _RunInRootFrame$Deter--\n          )\n            if (\n              sampleLines[namePropDescriptor] !==\n              controlLines[_RunInRootFrame$Deter]\n            ) {\n              if (1 !== namePropDescriptor || 1 !== _RunInRootFrame$Deter) {\n                do\n                  if (\n                    (namePropDescriptor--,\n                    _RunInRootFrame$Deter--,\n                    0 > _RunInRootFrame$Deter ||\n                      sampleLines[namePropDescriptor] !==\n                        controlLines[_RunInRootFrame$Deter])\n                  ) {\n                    var _frame =\n                      \"\\n\" +\n                      sampleLines[namePropDescriptor].replace(\n                        \" at new \",\n                        \" at \"\n                      );\n                    fn.displayName &&\n                      _frame.includes(\"<anonymous>\") &&\n                      (_frame = _frame.replace(\"<anonymous>\", fn.displayName));\n                    \"function\" === typeof fn &&\n                      componentFrameCache.set(fn, _frame);\n                    return _frame;\n                  }\n                while (1 <= namePropDescriptor && 0 <= _RunInRootFrame$Deter);\n              }\n              break;\n            }\n        }\n      } finally {\n        (reentry = !1),\n          (ReactSharedInternals.H = previousDispatcher),\n          reenableLogs(),\n          (Error.prepareStackTrace = frame);\n      }\n      sampleLines = (sampleLines = fn ? fn.displayName || fn.name : \"\")\n        ? describeBuiltInComponentFrame(sampleLines)\n        : \"\";\n      \"function\" === typeof fn && componentFrameCache.set(fn, sampleLines);\n      return sampleLines;\n    }\n    function describeUnknownElementTypeFrameInDEV(type) {\n      if (null == type) return \"\";\n      if (\"function\" === typeof type) {\n        var prototype = type.prototype;\n        return describeNativeComponentFrame(\n          type,\n          !(!prototype || !prototype.isReactComponent)\n        );\n      }\n      if (\"string\" === typeof type) return describeBuiltInComponentFrame(type);\n      switch (type) {\n        case REACT_SUSPENSE_TYPE:\n          return describeBuiltInComponentFrame(\"Suspense\");\n        case REACT_SUSPENSE_LIST_TYPE:\n          return describeBuiltInComponentFrame(\"SuspenseList\");\n      }\n      if (\"object\" === typeof type)\n        switch (type.$$typeof) {\n          case REACT_FORWARD_REF_TYPE:\n            return (type = describeNativeComponentFrame(type.render, !1)), type;\n          case REACT_MEMO_TYPE:\n            return describeUnknownElementTypeFrameInDEV(type.type);\n          case REACT_LAZY_TYPE:\n            prototype = type._payload;\n            type = type._init;\n            try {\n              return describeUnknownElementTypeFrameInDEV(type(prototype));\n            } catch (x) {}\n        }\n      return \"\";\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(type, key, self, source, owner, props) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      if (\n        \"string\" === typeof type ||\n        \"function\" === typeof type ||\n        type === REACT_FRAGMENT_TYPE ||\n        type === REACT_PROFILER_TYPE ||\n        type === REACT_STRICT_MODE_TYPE ||\n        type === REACT_SUSPENSE_TYPE ||\n        type === REACT_SUSPENSE_LIST_TYPE ||\n        type === REACT_OFFSCREEN_TYPE ||\n        (\"object\" === typeof type &&\n          null !== type &&\n          (type.$$typeof === REACT_LAZY_TYPE ||\n            type.$$typeof === REACT_MEMO_TYPE ||\n            type.$$typeof === REACT_CONTEXT_TYPE ||\n            type.$$typeof === REACT_CONSUMER_TYPE ||\n            type.$$typeof === REACT_FORWARD_REF_TYPE ||\n            type.$$typeof === REACT_CLIENT_REFERENCE$1 ||\n            void 0 !== type.getModuleId))\n      ) {\n        var children = config.children;\n        if (void 0 !== children)\n          if (isStaticChildren)\n            if (isArrayImpl(children)) {\n              for (\n                isStaticChildren = 0;\n                isStaticChildren < children.length;\n                isStaticChildren++\n              )\n                validateChildKeys(children[isStaticChildren], type);\n              Object.freeze && Object.freeze(children);\n            } else\n              console.error(\n                \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n              );\n          else validateChildKeys(children, type);\n      } else {\n        children = \"\";\n        if (\n          void 0 === type ||\n          (\"object\" === typeof type &&\n            null !== type &&\n            0 === Object.keys(type).length)\n        )\n          children +=\n            \" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\";\n        null === type\n          ? (isStaticChildren = \"null\")\n          : isArrayImpl(type)\n            ? (isStaticChildren = \"array\")\n            : void 0 !== type && type.$$typeof === REACT_ELEMENT_TYPE\n              ? ((isStaticChildren =\n                  \"<\" +\n                  (getComponentNameFromType(type.type) || \"Unknown\") +\n                  \" />\"),\n                (children =\n                  \" Did you accidentally export a JSX literal instead of a component?\"))\n              : (isStaticChildren = typeof type);\n        console.error(\n          \"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\",\n          isStaticChildren,\n          children\n        );\n      }\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(type, children, self, source, getOwner(), maybeKey);\n    }\n    function validateChildKeys(node, parentType) {\n      if (\n        \"object\" === typeof node &&\n        node &&\n        node.$$typeof !== REACT_CLIENT_REFERENCE\n      )\n        if (isArrayImpl(node))\n          for (var i = 0; i < node.length; i++) {\n            var child = node[i];\n            isValidElement(child) && validateExplicitKey(child, parentType);\n          }\n        else if (isValidElement(node))\n          node._store && (node._store.validated = 1);\n        else if (\n          (null === node || \"object\" !== typeof node\n            ? (i = null)\n            : ((i =\n                (MAYBE_ITERATOR_SYMBOL && node[MAYBE_ITERATOR_SYMBOL]) ||\n                node[\"@@iterator\"]),\n              (i = \"function\" === typeof i ? i : null)),\n          \"function\" === typeof i &&\n            i !== node.entries &&\n            ((i = i.call(node)), i !== node))\n        )\n          for (; !(node = i.next()).done; )\n            isValidElement(node.value) &&\n              validateExplicitKey(node.value, parentType);\n    }\n    function isValidElement(object) {\n      return (\n        \"object\" === typeof object &&\n        null !== object &&\n        object.$$typeof === REACT_ELEMENT_TYPE\n      );\n    }\n    function validateExplicitKey(element, parentType) {\n      if (\n        element._store &&\n        !element._store.validated &&\n        null == element.key &&\n        ((element._store.validated = 1),\n        (parentType = getCurrentComponentErrorInfo(parentType)),\n        !ownerHasKeyUseWarning[parentType])\n      ) {\n        ownerHasKeyUseWarning[parentType] = !0;\n        var childOwner = \"\";\n        element &&\n          null != element._owner &&\n          element._owner !== getOwner() &&\n          ((childOwner = null),\n          \"number\" === typeof element._owner.tag\n            ? (childOwner = getComponentNameFromType(element._owner.type))\n            : \"string\" === typeof element._owner.name &&\n              (childOwner = element._owner.name),\n          (childOwner = \" It was passed a child from \" + childOwner + \".\"));\n        var prevGetCurrentStack = ReactSharedInternals.getCurrentStack;\n        ReactSharedInternals.getCurrentStack = function () {\n          var stack = describeUnknownElementTypeFrameInDEV(element.type);\n          prevGetCurrentStack && (stack += prevGetCurrentStack() || \"\");\n          return stack;\n        };\n        console.error(\n          'Each child in a list should have a unique \"key\" prop.%s%s See https://react.dev/link/warning-keys for more information.',\n          parentType,\n          childOwner\n        );\n        ReactSharedInternals.getCurrentStack = prevGetCurrentStack;\n      }\n    }\n    function getCurrentComponentErrorInfo(parentType) {\n      var info = \"\",\n        owner = getOwner();\n      owner &&\n        (owner = getComponentNameFromType(owner.type)) &&\n        (info = \"\\n\\nCheck the render method of `\" + owner + \"`.\");\n      info ||\n        ((parentType = getComponentNameFromType(parentType)) &&\n          (info =\n            \"\\n\\nCheck the top-level render call using <\" + parentType + \">.\"));\n      return info;\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_OFFSCREEN_TYPE = Symbol.for(\"react.offscreen\"),\n      MAYBE_ITERATOR_SYMBOL = Symbol.iterator,\n      REACT_CLIENT_REFERENCE$2 = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      assign = Object.assign,\n      REACT_CLIENT_REFERENCE$1 = Symbol.for(\"react.client.reference\"),\n      isArrayImpl = Array.isArray,\n      disabledDepth = 0,\n      prevLog,\n      prevInfo,\n      prevWarn,\n      prevError,\n      prevGroup,\n      prevGroupCollapsed,\n      prevGroupEnd;\n    disabledLog.__reactDisabledLog = !0;\n    var prefix,\n      suffix,\n      reentry = !1;\n    var componentFrameCache = new (\n      \"function\" === typeof WeakMap ? WeakMap : Map\n    )();\n    var REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var didWarnAboutKeySpread = {},\n      ownerHasKeyUseWarning = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      return jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self);\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,2BACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO,CAAC,KAAK,WAAW,IAAI,SAAS,IAAI;YAC3C,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,eAAe;IACxB,SAAS;QACP,IAAI,MAAM,eAAe;YACvB,UAAU,QAAQ,GAAG;YACrB,WAAW,QAAQ,IAAI;YACvB,WAAW,QAAQ,IAAI;YACvB,YAAY,QAAQ,KAAK;YACzB,YAAY,QAAQ,KAAK;YACzB,qBAAqB,QAAQ,cAAc;YAC3C,eAAe,QAAQ,QAAQ;YAC/B,IAAI,QAAQ;gBACV,cAAc,CAAC;gBACf,YAAY,CAAC;gBACb,OAAO;gBACP,UAAU,CAAC;YACb;YACA,OAAO,gBAAgB,CAAC,SAAS;gBAC/B,MAAM;gBACN,KAAK;gBACL,MAAM;gBACN,OAAO;gBACP,OAAO;gBACP,gBAAgB;gBAChB,UAAU;YACZ;QACF;QACA;IACF;IACA,SAAS;QACP;QACA,IAAI,MAAM,eAAe;YACvB,IAAI,QAAQ;gBAAE,cAAc,CAAC;gBAAG,YAAY,CAAC;gBAAG,UAAU,CAAC;YAAE;YAC7D,OAAO,gBAAgB,CAAC,SAAS;gBAC/B,KAAK,OAAO,CAAC,GAAG,OAAO;oBAAE,OAAO;gBAAQ;gBACxC,MAAM,OAAO,CAAC,GAAG,OAAO;oBAAE,OAAO;gBAAS;gBAC1C,MAAM,OAAO,CAAC,GAAG,OAAO;oBAAE,OAAO;gBAAS;gBAC1C,OAAO,OAAO,CAAC,GAAG,OAAO;oBAAE,OAAO;gBAAU;gBAC5C,OAAO,OAAO,CAAC,GAAG,OAAO;oBAAE,OAAO;gBAAU;gBAC5C,gBAAgB,OAAO,CAAC,GAAG,OAAO;oBAAE,OAAO;gBAAmB;gBAC9D,UAAU,OAAO,CAAC,GAAG,OAAO;oBAAE,OAAO;gBAAa;YACpD;QACF;QACA,IAAI,iBACF,QAAQ,KAAK,CACX;IAEN;IACA,SAAS,8BAA8B,IAAI;QACzC,IAAI,KAAK,MAAM,QACb,IAAI;YACF,MAAM;QACR,EAAE,OAAO,GAAG;YACV,IAAI,QAAQ,EAAE,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC;YACjC,SAAS,AAAC,SAAS,KAAK,CAAC,EAAE,IAAK;YAChC,SACE,CAAC,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,cACjB,mBACA,CAAC,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,OACnB,iBACA;QACV;QACF,OAAO,OAAO,SAAS,OAAO;IAChC;IACA,SAAS,6BAA6B,EAAE,EAAE,SAAS;QACjD,IAAI,CAAC,MAAM,SAAS,OAAO;QAC3B,IAAI,QAAQ,oBAAoB,GAAG,CAAC;QACpC,IAAI,KAAK,MAAM,OAAO,OAAO;QAC7B,UAAU,CAAC;QACX,QAAQ,MAAM,iBAAiB;QAC/B,MAAM,iBAAiB,GAAG,KAAK;QAC/B,IAAI,qBAAqB;QACzB,qBAAqB,qBAAqB,CAAC;QAC3C,qBAAqB,CAAC,GAAG;QACzB;QACA,IAAI;YACF,IAAI,iBAAiB;gBACnB,6BAA6B;oBAC3B,IAAI;wBACF,IAAI,WAAW;4BACb,IAAI,OAAO;gCACT,MAAM;4BACR;4BACA,OAAO,cAAc,CAAC,KAAK,SAAS,EAAE,SAAS;gCAC7C,KAAK;oCACH,MAAM;gCACR;4BACF;4BACA,IAAI,aAAa,OAAO,WAAW,QAAQ,SAAS,EAAE;gCACpD,IAAI;oCACF,QAAQ,SAAS,CAAC,MAAM,EAAE;gCAC5B,EAAE,OAAO,GAAG;oCACV,IAAI,UAAU;gCAChB;gCACA,QAAQ,SAAS,CAAC,IAAI,EAAE,EAAE;4BAC5B,OAAO;gCACL,IAAI;oCACF,KAAK,IAAI;gCACX,EAAE,OAAO,KAAK;oCACZ,UAAU;gCACZ;gCACA,GAAG,IAAI,CAAC,KAAK,SAAS;4BACxB;wBACF,OAAO;4BACL,IAAI;gCACF,MAAM;4BACR,EAAE,OAAO,KAAK;gCACZ,UAAU;4BACZ;4BACA,CAAC,OAAO,IAAI,KACV,eAAe,OAAO,KAAK,KAAK,IAChC,KAAK,KAAK,CAAC,YAAa;wBAC5B;oBACF,EAAE,OAAO,QAAQ;wBACf,IAAI,UAAU,WAAW,aAAa,OAAO,OAAO,KAAK,EACvD,OAAO;4BAAC,OAAO,KAAK;4BAAE,QAAQ,KAAK;yBAAC;oBACxC;oBACA,OAAO;wBAAC;wBAAM;qBAAK;gBACrB;YACF;YACA,eAAe,2BAA2B,CAAC,WAAW,GACpD;YACF,IAAI,qBAAqB,OAAO,wBAAwB,CACtD,eAAe,2BAA2B,EAC1C;YAEF,sBACE,mBAAmB,YAAY,IAC/B,OAAO,cAAc,CACnB,eAAe,2BAA2B,EAC1C,QACA;gBAAE,OAAO;YAA8B;YAE3C,IAAI,wBACA,eAAe,2BAA2B,IAC5C,cAAc,qBAAqB,CAAC,EAAE,EACtC,eAAe,qBAAqB,CAAC,EAAE;YACzC,IAAI,eAAe,cAAc;gBAC/B,IAAI,cAAc,YAAY,KAAK,CAAC,OAClC,eAAe,aAAa,KAAK,CAAC;gBACpC,IACE,wBAAwB,qBAAqB,GAC7C,qBAAqB,YAAY,MAAM,IACvC,CAAC,WAAW,CAAC,mBAAmB,CAAC,QAAQ,CACvC,gCAIF;gBACF,MAEE,wBAAwB,aAAa,MAAM,IAC3C,CAAC,YAAY,CAAC,sBAAsB,CAAC,QAAQ,CAC3C,gCAIF;gBACF,IACE,uBAAuB,YAAY,MAAM,IACzC,0BAA0B,aAAa,MAAM,EAE7C,IACE,qBAAqB,YAAY,MAAM,GAAG,GACxC,wBAAwB,aAAa,MAAM,GAAG,GAChD,KAAK,sBACL,KAAK,yBACL,WAAW,CAAC,mBAAmB,KAC7B,YAAY,CAAC,sBAAsB,EAGrC;gBACJ,MAEE,KAAK,sBAAsB,KAAK,uBAChC,sBAAsB,wBAEtB,IACE,WAAW,CAAC,mBAAmB,KAC/B,YAAY,CAAC,sBAAsB,EACnC;oBACA,IAAI,MAAM,sBAAsB,MAAM,uBAAuB;wBAC3D,GACE,IACG,sBACD,yBACA,IAAI,yBACF,WAAW,CAAC,mBAAmB,KAC7B,YAAY,CAAC,sBAAsB,EACvC;4BACA,IAAI,SACF,OACA,WAAW,CAAC,mBAAmB,CAAC,OAAO,CACrC,YACA;4BAEJ,GAAG,WAAW,IACZ,OAAO,QAAQ,CAAC,kBAChB,CAAC,SAAS,OAAO,OAAO,CAAC,eAAe,GAAG,WAAW,CAAC;4BACzD,eAAe,OAAO,MACpB,oBAAoB,GAAG,CAAC,IAAI;4BAC9B,OAAO;wBACT;+BACK,KAAK,sBAAsB,KAAK,sBAAuB;oBAChE;oBACA;gBACF;YACJ;QACF,SAAU;YACP,UAAU,CAAC,GACT,qBAAqB,CAAC,GAAG,oBAC1B,gBACC,MAAM,iBAAiB,GAAG;QAC/B;QACA,cAAc,CAAC,cAAc,KAAK,GAAG,WAAW,IAAI,GAAG,IAAI,GAAG,EAAE,IAC5D,8BAA8B,eAC9B;QACJ,eAAe,OAAO,MAAM,oBAAoB,GAAG,CAAC,IAAI;QACxD,OAAO;IACT;IACA,SAAS,qCAAqC,IAAI;QAChD,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MAAM;YAC9B,IAAI,YAAY,KAAK,SAAS;YAC9B,OAAO,6BACL,MACA,CAAC,CAAC,CAAC,aAAa,CAAC,UAAU,gBAAgB;QAE/C;QACA,IAAI,aAAa,OAAO,MAAM,OAAO,8BAA8B;QACnE,OAAQ;YACN,KAAK;gBACH,OAAO,8BAA8B;YACvC,KAAK;gBACH,OAAO,8BAA8B;QACzC;QACA,IAAI,aAAa,OAAO,MACtB,OAAQ,KAAK,QAAQ;YACnB,KAAK;gBACH,OAAO,AAAC,OAAO,6BAA6B,KAAK,MAAM,EAAE,CAAC,IAAK;YACjE,KAAK;gBACH,OAAO,qCAAqC,KAAK,IAAI;YACvD,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,qCAAqC,KAAK;gBACnD,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aAAa,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK;QACzD,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IACE,aAAa,OAAO,QACpB,eAAe,OAAO,QACtB,SAAS,uBACT,SAAS,uBACT,SAAS,0BACT,SAAS,uBACT,SAAS,4BACT,SAAS,wBACR,aAAa,OAAO,QACnB,SAAS,QACT,CAAC,KAAK,QAAQ,KAAK,mBACjB,KAAK,QAAQ,KAAK,mBAClB,KAAK,QAAQ,KAAK,sBAClB,KAAK,QAAQ,KAAK,uBAClB,KAAK,QAAQ,KAAK,0BAClB,KAAK,QAAQ,KAAK,4BAClB,KAAK,MAAM,KAAK,WAAW,GAC/B;YACA,IAAI,WAAW,OAAO,QAAQ;YAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;gBACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB,EAAE;gBAChD,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;YACjC,OACE,QAAQ,KAAK,CACX;iBAED,kBAAkB,UAAU;QACrC,OAAO;YACL,WAAW;YACX,IACE,KAAK,MAAM,QACV,aAAa,OAAO,QACnB,SAAS,QACT,MAAM,OAAO,IAAI,CAAC,MAAM,MAAM,EAEhC,YACE;YACJ,SAAS,OACJ,mBAAmB,SACpB,YAAY,QACT,mBAAmB,UACpB,KAAK,MAAM,QAAQ,KAAK,QAAQ,KAAK,qBACnC,CAAC,AAAC,mBACA,MACA,CAAC,yBAAyB,KAAK,IAAI,KAAK,SAAS,IACjD,OACD,WACC,oEAAqE,IACtE,mBAAmB,OAAO;YACnC,QAAQ,KAAK,CACX,2IACA,kBACA;QAEJ;QACA,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aAAa,MAAM,UAAU,MAAM,QAAQ,YAAY;IAChE;IACA,SAAS,kBAAkB,IAAI,EAAE,UAAU;QACzC,IACE,aAAa,OAAO,QACpB,QACA,KAAK,QAAQ,KAAK,wBAElB;YAAA,IAAI,YAAY,OACd,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;gBACpC,IAAI,QAAQ,IAAI,CAAC,EAAE;gBACnB,eAAe,UAAU,oBAAoB,OAAO;YACtD;iBACG,IAAI,eAAe,OACtB,KAAK,MAAM,IAAI,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;iBACtC,IACF,SAAS,QAAQ,aAAa,OAAO,OACjC,IAAI,OACL,CAAC,AAAC,IACA,AAAC,yBAAyB,IAAI,CAAC,sBAAsB,IACrD,IAAI,CAAC,aAAa,EACnB,IAAI,eAAe,OAAO,IAAI,IAAI,IAAK,GAC5C,eAAe,OAAO,KACpB,MAAM,KAAK,OAAO,IAClB,CAAC,AAAC,IAAI,EAAE,IAAI,CAAC,OAAQ,MAAM,IAAI,GAEjC,MAAO,CAAC,CAAC,OAAO,EAAE,IAAI,EAAE,EAAE,IAAI,EAC5B,eAAe,KAAK,KAAK,KACvB,oBAAoB,KAAK,KAAK,EAAE;QAAW;IACrD;IACA,SAAS,eAAe,MAAM;QAC5B,OACE,aAAa,OAAO,UACpB,SAAS,UACT,OAAO,QAAQ,KAAK;IAExB;IACA,SAAS,oBAAoB,OAAO,EAAE,UAAU;QAC9C,IACE,QAAQ,MAAM,IACd,CAAC,QAAQ,MAAM,CAAC,SAAS,IACzB,QAAQ,QAAQ,GAAG,IACnB,CAAC,AAAC,QAAQ,MAAM,CAAC,SAAS,GAAG,GAC5B,aAAa,6BAA6B,aAC3C,CAAC,qBAAqB,CAAC,WAAW,GAClC;YACA,qBAAqB,CAAC,WAAW,GAAG,CAAC;YACrC,IAAI,aAAa;YACjB,WACE,QAAQ,QAAQ,MAAM,IACtB,QAAQ,MAAM,KAAK,cACnB,CAAC,AAAC,aAAa,MACf,aAAa,OAAO,QAAQ,MAAM,CAAC,GAAG,GACjC,aAAa,yBAAyB,QAAQ,MAAM,CAAC,IAAI,IAC1D,aAAa,OAAO,QAAQ,MAAM,CAAC,IAAI,IACvC,CAAC,aAAa,QAAQ,MAAM,CAAC,IAAI,GACpC,aAAa,iCAAiC,aAAa,GAAI;YAClE,IAAI,sBAAsB,qBAAqB,eAAe;YAC9D,qBAAqB,eAAe,GAAG;gBACrC,IAAI,QAAQ,qCAAqC,QAAQ,IAAI;gBAC7D,uBAAuB,CAAC,SAAS,yBAAyB,EAAE;gBAC5D,OAAO;YACT;YACA,QAAQ,KAAK,CACX,2HACA,YACA;YAEF,qBAAqB,eAAe,GAAG;QACzC;IACF;IACA,SAAS,6BAA6B,UAAU;QAC9C,IAAI,OAAO,IACT,QAAQ;QACV,SACE,CAAC,QAAQ,yBAAyB,MAAM,IAAI,CAAC,KAC7C,CAAC,OAAO,qCAAqC,QAAQ,IAAI;QAC3D,QACG,CAAC,aAAa,yBAAyB,WAAW,KACjD,CAAC,OACC,gDAAgD,aAAa,IAAI;QACvE,OAAO;IACT;IACA,IAAI,qHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC;IACnC,OAAO,GAAG,CAAC;IACX,IAAI,sBAAsB,OAAO,GAAG,CAAC,mBACnC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,uBAAuB,OAAO,GAAG,CAAC,oBAClC,wBAAwB,OAAO,QAAQ,EACvC,2BAA2B,OAAO,GAAG,CAAC,2BACtC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,SAAS,OAAO,MAAM,EACtB,2BAA2B,OAAO,GAAG,CAAC,2BACtC,cAAc,MAAM,OAAO,EAC3B,gBAAgB,GAChB,SACA,UACA,UACA,WACA,WACA,oBACA;IACF,YAAY,kBAAkB,GAAG,CAAC;IAClC,IAAI,QACF,QACA,UAAU,CAAC;IACb,IAAI,sBAAsB,IAAI,CAC5B,eAAe,OAAO,UAAU,UAAU,GAC5C;IACA,IAAI,yBAAyB,OAAO,GAAG,CAAC,2BACtC;IACF,IAAI,yBAAyB,CAAC;IAC9B,IAAI,wBAAwB,CAAC,GAC3B,wBAAwB,CAAC;IAC3B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,OAAO,WAAW,MAAM,QAAQ,UAAU,kBAAkB,QAAQ;IACtE;AACF", "ignoreList": [0]}}, {"offset": {"line": 1892, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1897, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/T_02/cursor-clone/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0]}}, {"offset": {"line": 1904, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}