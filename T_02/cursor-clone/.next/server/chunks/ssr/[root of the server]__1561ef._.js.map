{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/T_02/cursor-clone/src/components/icons.tsx"], "sourcesContent": ["// Icon components for the Cursor website\nexport const CursorLogo = ({ className = \"w-8 h-8\" }: { className?: string }) => (\n  <div className={`bg-black rounded-md flex items-center justify-center ${className}`}>\n    <svg viewBox=\"0 0 24 24\" className=\"w-5 h-5 text-white\" fill=\"currentColor\">\n      <path d=\"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5\"/>\n    </svg>\n  </div>\n);\n\nexport const MenuIcon = ({ className = \"w-6 h-6\" }: { className?: string }) => (\n  <svg className={className} fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n  </svg>\n);\n\nexport const CloseIcon = ({ className = \"w-6 h-6\" }: { className?: string }) => (\n  <svg className={className} fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n  </svg>\n);\n\nexport const DownloadIcon = ({ className = \"w-5 h-5\" }: { className?: string }) => (\n  <svg className={className} fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n  </svg>\n);\n\nexport const ArrowRightIcon = ({ className = \"w-5 h-5\" }: { className?: string }) => (\n  <svg className={className} fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 8l4 4m0 0l-4 4m4-4H3\" />\n  </svg>\n);\n\nexport const CodeIcon = ({ className = \"w-8 h-8\" }: { className?: string }) => (\n  <svg className={className} fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4\" />\n  </svg>\n);\n\nexport const BrainIcon = ({ className = \"w-8 h-8\" }: { className?: string }) => (\n  <svg className={className} fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\" />\n  </svg>\n);\n\nexport const ShieldIcon = ({ className = \"w-8 h-8\" }: { className?: string }) => (\n  <svg className={className} fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\" />\n  </svg>\n);\n\nexport const SparkleIcon = ({ className = \"w-8 h-8\" }: { className?: string }) => (\n  <svg className={className} fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z\" />\n  </svg>\n);\n\nexport const TwitterIcon = ({ className = \"w-5 h-5\" }: { className?: string }) => (\n  <svg className={className} fill=\"currentColor\" viewBox=\"0 0 24 24\">\n    <path d=\"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z\"/>\n  </svg>\n);\n\nexport const GitHubIcon = ({ className = \"w-5 h-5\" }: { className?: string }) => (\n  <svg className={className} fill=\"currentColor\" viewBox=\"0 0 24 24\">\n    <path d=\"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z\"/>\n  </svg>\n);\n\nexport const YouTubeIcon = ({ className = \"w-5 h-5\" }: { className?: string }) => (\n  <svg className={className} fill=\"currentColor\" viewBox=\"0 0 24 24\">\n    <path d=\"M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z\"/>\n  </svg>\n);\n\nexport const RedditIcon = ({ className = \"w-5 h-5\" }: { className?: string }) => (\n  <svg className={className} fill=\"currentColor\" viewBox=\"0 0 24 24\">\n    <path d=\"M12 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0zm5.01 4.744c.688 0 1.25.561 1.25 1.249a1.25 1.25 0 0 1-2.498.056l-2.597-.547-.8 3.747c1.824.07 3.48.632 4.674 1.488.308-.309.73-.491 1.207-.491.968 0 1.754.786 1.754 1.754 0 .716-.435 1.333-1.01 1.614a3.111 3.111 0 0 1 .042.52c0 2.694-3.13 4.87-7.004 4.87-3.874 0-7.004-2.176-7.004-4.87 0-.183.015-.366.043-.534A1.748 1.748 0 0 1 4.028 12c0-.968.786-1.754 1.754-1.754.463 0 .898.196 1.207.49 1.207-.883 2.878-1.43 4.744-1.487l.885-4.182a.342.342 0 0 1 .14-.197.35.35 0 0 1 .238-.042l2.906.617a1.214 1.214 0 0 1 1.108-.701zM9.25 12C8.561 12 8 12.562 8 13.25c0 .687.561 1.248 1.25 1.248.687 0 1.248-.561 1.248-1.249 0-.688-.561-1.249-1.249-1.249zm5.5 0c-.687 0-1.248.561-1.248 1.25 0 .687.561 1.248 1.249 1.248.688 0 1.249-.561 1.249-1.249 0-.687-.562-1.249-1.25-1.249zm-5.466 3.99a.327.327 0 0 0-.231.094.33.33 0 0 0 0 .463c.842.842 2.484.913 2.961.913.477 0 2.105-.056 2.961-.913a.361.361 0 0 0 .029-.463.33.33 0 0 0-.464 0c-.547.533-1.684.73-2.512.73-.828 0-1.979-.196-2.512-.73a.326.326 0 0 0-.232-.095z\"/>\n  </svg>\n);\n"], "names": [], "mappings": "AAAA,yCAAyC;;;;;;;;;;;;;;;;;;AAClC,MAAM,aAAa,CAAC,EAAE,YAAY,SAAS,EAA0B,iBAC1E,8OAAC;QAAI,WAAW,CAAC,qDAAqD,EAAE,WAAW;kBACjF,cAAA,8OAAC;YAAI,SAAQ;YAAY,WAAU;YAAqB,MAAK;sBAC3D,cAAA,8OAAC;gBAAK,GAAE;;;;;;;;;;;;;;;;AAKP,MAAM,WAAW,CAAC,EAAE,YAAY,SAAS,EAA0B,iBACxE,8OAAC;QAAI,WAAW;QAAW,MAAK;QAAO,SAAQ;QAAY,QAAO;kBAChE,cAAA,8OAAC;YAAK,eAAc;YAAQ,gBAAe;YAAQ,aAAa;YAAG,GAAE;;;;;;;;;;;AAIlE,MAAM,YAAY,CAAC,EAAE,YAAY,SAAS,EAA0B,iBACzE,8OAAC;QAAI,WAAW;QAAW,MAAK;QAAO,SAAQ;QAAY,QAAO;kBAChE,cAAA,8OAAC;YAAK,eAAc;YAAQ,gBAAe;YAAQ,aAAa;YAAG,GAAE;;;;;;;;;;;AAIlE,MAAM,eAAe,CAAC,EAAE,YAAY,SAAS,EAA0B,iBAC5E,8OAAC;QAAI,WAAW;QAAW,MAAK;QAAO,SAAQ;QAAY,QAAO;kBAChE,cAAA,8OAAC;YAAK,eAAc;YAAQ,gBAAe;YAAQ,aAAa;YAAG,GAAE;;;;;;;;;;;AAIlE,MAAM,iBAAiB,CAAC,EAAE,YAAY,SAAS,EAA0B,iBAC9E,8OAAC;QAAI,WAAW;QAAW,MAAK;QAAO,SAAQ;QAAY,QAAO;kBAChE,cAAA,8OAAC;YAAK,eAAc;YAAQ,gBAAe;YAAQ,aAAa;YAAG,GAAE;;;;;;;;;;;AAIlE,MAAM,WAAW,CAAC,EAAE,YAAY,SAAS,EAA0B,iBACxE,8OAAC;QAAI,WAAW;QAAW,MAAK;QAAO,SAAQ;QAAY,QAAO;kBAChE,cAAA,8OAAC;YAAK,eAAc;YAAQ,gBAAe;YAAQ,aAAa;YAAG,GAAE;;;;;;;;;;;AAIlE,MAAM,YAAY,CAAC,EAAE,YAAY,SAAS,EAA0B,iBACzE,8OAAC;QAAI,WAAW;QAAW,MAAK;QAAO,SAAQ;QAAY,QAAO;kBAChE,cAAA,8OAAC;YAAK,eAAc;YAAQ,gBAAe;YAAQ,aAAa;YAAG,GAAE;;;;;;;;;;;AAIlE,MAAM,aAAa,CAAC,EAAE,YAAY,SAAS,EAA0B,iBAC1E,8OAAC;QAAI,WAAW;QAAW,MAAK;QAAO,SAAQ;QAAY,QAAO;kBAChE,cAAA,8OAAC;YAAK,eAAc;YAAQ,gBAAe;YAAQ,aAAa;YAAG,GAAE;;;;;;;;;;;AAIlE,MAAM,cAAc,CAAC,EAAE,YAAY,SAAS,EAA0B,iBAC3E,8OAAC;QAAI,WAAW;QAAW,MAAK;QAAO,SAAQ;QAAY,QAAO;kBAChE,cAAA,8OAAC;YAAK,eAAc;YAAQ,gBAAe;YAAQ,aAAa;YAAG,GAAE;;;;;;;;;;;AAIlE,MAAM,cAAc,CAAC,EAAE,YAAY,SAAS,EAA0B,iBAC3E,8OAAC;QAAI,WAAW;QAAW,MAAK;QAAe,SAAQ;kBACrD,cAAA,8OAAC;YAAK,GAAE;;;;;;;;;;;AAIL,MAAM,aAAa,CAAC,EAAE,YAAY,SAAS,EAA0B,iBAC1E,8OAAC;QAAI,WAAW;QAAW,MAAK;QAAe,SAAQ;kBACrD,cAAA,8OAAC;YAAK,GAAE;;;;;;;;;;;AAIL,MAAM,cAAc,CAAC,EAAE,YAAY,SAAS,EAA0B,iBAC3E,8OAAC;QAAI,WAAW;QAAW,MAAK;QAAe,SAAQ;kBACrD,cAAA,8OAAC;YAAK,GAAE;;;;;;;;;;;AAIL,MAAM,aAAa,CAAC,EAAE,YAAY,SAAS,EAA0B,iBAC1E,8OAAC;QAAI,WAAW;QAAW,MAAK;QAAe,SAAQ;kBACrD,cAAA,8OAAC;YAAK,GAAE"}}, {"offset": {"line": 280, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 299, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/T_02/cursor-clone/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n    } else if (process.env.TURBOPACK) {\n      module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n    } else {\n      module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n    } else if (process.env.TURBOPACK) {\n      module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n    } else {\n      module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAQzC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1CJ,OAAOC,OAAO,GAAGC,QAAQ;QAC3B,OAAO,IAAIL,QAAQC,GAAG,CAACO,SAAS,EAAE;;QAIlC;IACF;AACF", "ignoreList": [0]}}, {"offset": {"line": 313, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 318, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/T_02/cursor-clone/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,0HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0]}}, {"offset": {"line": 320, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 325, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/T_02/cursor-clone/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,0HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0]}}, {"offset": {"line": 327, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}