{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/T_02/cursor-clone/src/app/page.tsx"], "sourcesContent": ["import Image from \"next/image\";\n\n// Navigation Component\nfunction Navigation() {\n  return (\n    <nav className=\"fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <div className=\"w-8 h-8 bg-black rounded-md flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">C</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Navigation Links */}\n          <div className=\"hidden md:block\">\n            <div className=\"ml-10 flex items-baseline space-x-8\">\n              <a href=\"/pricing\" className=\"text-gray-700 hover:text-black px-3 py-2 text-sm font-medium\">\n                定价\n              </a>\n              <a href=\"/features\" className=\"text-gray-700 hover:text-black px-3 py-2 text-sm font-medium\">\n                功能\n              </a>\n              <a href=\"/enterprise\" className=\"text-gray-700 hover:text-black px-3 py-2 text-sm font-medium\">\n                企业\n              </a>\n              <a href=\"/blog\" className=\"text-gray-700 hover:text-black px-3 py-2 text-sm font-medium\">\n                博客\n              </a>\n              <a href=\"https://forum.cursor.com\" className=\"text-gray-700 hover:text-black px-3 py-2 text-sm font-medium\">\n                论坛\n              </a>\n              <a href=\"https://anysphere.inc/\" className=\"text-gray-700 hover:text-black px-3 py-2 text-sm font-medium\">\n                招聘\n              </a>\n            </div>\n          </div>\n\n          {/* Right side buttons */}\n          <div className=\"flex items-center space-x-4\">\n            <a href=\"/api/auth/login\" className=\"text-gray-700 hover:text-black px-3 py-2 text-sm font-medium\">\n              登录\n            </a>\n            <a\n              href=\"/downloads\"\n              className=\"bg-black text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-800 transition-colors\"\n            >\n              下载\n            </a>\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n}\n\n// Hero Section Component\nfunction HeroSection() {\n  return (\n    <section className=\"pt-32 pb-20 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-7xl mx-auto\">\n        <div className=\"text-center\">\n          {/* Hero Background Image */}\n          <div className=\"relative mb-12\">\n            <div className=\"absolute inset-0 bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 rounded-3xl opacity-20 blur-3xl\"></div>\n            <div className=\"relative bg-gray-50 rounded-3xl p-8 md:p-16\">\n              <h1 className=\"text-4xl md:text-6xl lg:text-7xl font-bold text-gray-900 mb-6\">\n                AI 代码编辑器\n              </h1>\n              <p className=\"text-xl md:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto\">\n                旨在让你获得超凡的生产力， Cursor 是使用 AI 编写代码的最佳方式。\n              </p>\n\n              {/* Download Buttons */}\n              <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center mb-12\">\n                <button className=\"bg-black text-white px-8 py-4 rounded-lg text-lg font-medium hover:bg-gray-800 transition-colors\">\n                  下载 MacOS\n                </button>\n                <a href=\"/downloads\" className=\"text-gray-600 hover:text-black text-lg underline\">\n                  所有下载\n                </a>\n              </div>\n\n              {/* Hero Image Placeholder */}\n              <div className=\"relative\">\n                <div className=\"bg-gray-200 rounded-xl h-64 md:h-96 flex items-center justify-center\">\n                  <span className=\"text-gray-500 text-lg\">Cursor 编辑器界面预览</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n\n// Company Trust Section\nfunction CompanyTrustSection() {\n  const companies = [\n    \"Johnson & Johnson\", \"OpenAI\", \"Stripe\", \"Samsung\", \"Instacart\",\n    \"Perplexity\", \"Ramp\", \"Shopify\", \"US Foods\", \"MercadoLibre\"\n  ];\n\n  return (\n    <section className=\"py-16 px-4 sm:px-6 lg:px-8 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-8\">由工程师信任</h2>\n          <div className=\"grid grid-cols-2 md:grid-cols-5 gap-8 items-center\">\n            {companies.map((company, index) => (\n              <div key={index} className=\"text-gray-600 font-medium text-sm md:text-base\">\n                {company}\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n\n// Features Section\nfunction FeaturesSection() {\n  return (\n    <section className=\"py-20 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-7xl mx-auto\">\n        {/* Feature 1 */}\n        <div className=\"mb-20\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              Tab，Tab，再来一次 Tab\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              Cursor 会根据上下文预测你的下一步编辑，让你轻松完成修改。\n            </p>\n          </div>\n          <div className=\"bg-gray-100 rounded-xl h-64 md:h-96 flex items-center justify-center\">\n            <span className=\"text-gray-500 text-lg\">Tab 补全功能演示</span>\n          </div>\n        </div>\n\n        {/* Feature 2 */}\n        <div className=\"mb-20\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              熟知你的代码库\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              从你的代码库或文档中获取答案，还能引用具体文件。 一键即可应用模型生成的代码。\n            </p>\n          </div>\n          <div className=\"bg-gray-100 rounded-xl h-64 md:h-96 flex items-center justify-center\">\n            <span className=\"text-gray-500 text-lg\">代码库理解功能演示</span>\n          </div>\n        </div>\n\n        {/* Feature 3 */}\n        <div className=\"mb-20\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              用自然语言编辑\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              借助 Cursor，你可以通过简单指令来编写或修改代码。 仅需一个提示，就能更新整段类或函数。\n            </p>\n          </div>\n          <div className=\"bg-gray-100 rounded-xl h-64 md:h-96 flex items-center justify-center\">\n            <span className=\"text-gray-500 text-lg\">自然语言编辑功能演示</span>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n\n// Product Features Cards Section\nfunction ProductFeaturesSection() {\n  const features = [\n    {\n      title: \"前沿智能\",\n      description: \"由定制化模型与前沿模型混合驱动，Cursor 既聪明又快速。\"\n    },\n    {\n      title: \"熟悉的体验\",\n      description: \"一键即可导入所有扩展、主题和快捷键绑定。\"\n    },\n    {\n      title: \"隐私选项\",\n      description: \"启用隐私模式后，你的代码不会储存于远程。Cursor 已通过 SOC 2 认证。\"\n    }\n  ];\n\n  return (\n    <section className=\"py-20 px-4 sm:px-6 lg:px-8 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n            更快构建软件\n          </h2>\n          <p className=\"text-xl text-gray-600 mb-8\">\n            智能、高速且熟悉，Cursor 是 AI 写代码的最佳选择。\n          </p>\n          <a href=\"/features\" className=\"text-blue-600 hover:text-blue-800 font-medium\">\n            更多功能\n          </a>\n        </div>\n\n        <div className=\"grid md:grid-cols-3 gap-8\">\n          {features.map((feature, index) => (\n            <div key={index} className=\"bg-white p-8 rounded-xl shadow-sm border border-gray-200\">\n              <h3 className=\"text-xl font-bold text-gray-900 mb-4\">{feature.title}</h3>\n              <p className=\"text-gray-600\">{feature.description}</p>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n}\n\n// Testimonials Section\nfunction TestimonialsSection() {\n  const testimonials = [\n    {\n      quote: \"Cursor 至少比 Copilot 提升两倍效率。 有了 AI 搭档编程非常棒，对我和团队而言都是极大的加速器。\",\n      author: \"Ben Bernard\",\n      company: \"Instacart\"\n    },\n    {\n      quote: \"写代码时，Cursor 的 Tab 补全有时就像魔法一样——大约四分之一的情况能完美猜中我想做什么。\",\n      author: \"Kevin Whinnery\",\n      company: \"OpenAI\"\n    },\n    {\n      quote: \"对我而言，Cursor 是近几年里工作流程提升最大的工具。\",\n      author: \"Sawyer Hood\",\n      company: \"Figma\"\n    }\n  ];\n\n  return (\n    <section className=\"py-20 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-7xl mx-auto\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n            深受全球顶尖开发者喜爱\n          </h2>\n          <p className=\"text-xl text-gray-600\">\n            世界各地的工程师都主动选择使用 Cursor。\n          </p>\n        </div>\n\n        <div className=\"grid md:grid-cols-3 gap-8\">\n          {testimonials.map((testimonial, index) => (\n            <div key={index} className=\"bg-gray-50 p-8 rounded-xl\">\n              <p className=\"text-gray-700 mb-6 italic\">\"{testimonial.quote}\"</p>\n              <div className=\"flex items-center\">\n                <div className=\"w-12 h-12 bg-gray-300 rounded-full mr-4\"></div>\n                <div>\n                  <div className=\"font-bold text-gray-900\">{testimonial.author}</div>\n                  <div className=\"text-gray-600\">{testimonial.company}</div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n}\n\n// Footer Component\nfunction Footer() {\n  return (\n    <footer className=\"bg-black text-white py-16 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-7xl mx-auto\">\n        {/* Call to Action */}\n        <div className=\"text-center mb-16\">\n          <div className=\"mb-8\">\n            <div className=\"w-16 h-16 bg-white rounded-lg flex items-center justify-center mx-auto mb-6\">\n              <span className=\"text-black font-bold text-xl\">C</span>\n            </div>\n          </div>\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-4\">立即试用 Cursor</h2>\n          <p className=\"text-xl text-gray-300 mb-8\">免费下载</p>\n          <div className=\"mb-8\">\n            <a href=\"mailto:<EMAIL>\" className=\"text-gray-300 hover:text-white\">\n              <EMAIL>\n            </a>\n          </div>\n\n          {/* Social Links */}\n          <div className=\"flex justify-center space-x-6 mb-12\">\n            <a href=\"https://x.com/cursor_ai\" className=\"text-gray-300 hover:text-white\">\n              Twitter\n            </a>\n            <a href=\"https://github.com/getcursor/cursor\" className=\"text-gray-300 hover:text-white\">\n              GitHub\n            </a>\n            <a href=\"https://www.reddit.com/r/cursor\" className=\"text-gray-300 hover:text-white\">\n              Reddit\n            </a>\n            <a href=\"https://www.youtube.com/@cursor_ai\" className=\"text-gray-300 hover:text-white\">\n              YouTube\n            </a>\n          </div>\n        </div>\n\n        {/* Footer Links */}\n        <div className=\"grid md:grid-cols-4 gap-8 mb-12\">\n          <div>\n            <h3 className=\"font-bold mb-4\">Product</h3>\n            <ul className=\"space-y-2\">\n              <li><a href=\"/pricing\" className=\"text-gray-300 hover:text-white\">定价</a></li>\n              <li><a href=\"/features\" className=\"text-gray-300 hover:text-white\">Features</a></li>\n              <li><a href=\"/enterprise\" className=\"text-gray-300 hover:text-white\">Enterprise</a></li>\n              <li><a href=\"/downloads\" className=\"text-gray-300 hover:text-white\">下载</a></li>\n              <li><a href=\"/students\" className=\"text-gray-300 hover:text-white\">Students</a></li>\n            </ul>\n          </div>\n\n          <div>\n            <h3 className=\"font-bold mb-4\">Resources</h3>\n            <ul className=\"space-y-2\">\n              <li><a href=\"https://docs.cursor.com\" className=\"text-gray-300 hover:text-white\">文档</a></li>\n              <li><a href=\"/blog\" className=\"text-gray-300 hover:text-white\">Blog</a></li>\n              <li><a href=\"https://forum.cursor.com\" className=\"text-gray-300 hover:text-white\">论坛</a></li>\n              <li><a href=\"/changelog\" className=\"text-gray-300 hover:text-white\">更新日志</a></li>\n            </ul>\n          </div>\n\n          <div>\n            <h3 className=\"font-bold mb-4\">Company</h3>\n            <ul className=\"space-y-2\">\n              <li><a href=\"https://anysphere.inc\" className=\"text-gray-300 hover:text-white\">公司</a></li>\n              <li><a href=\"https://anysphere.inc\" className=\"text-gray-300 hover:text-white\">招聘</a></li>\n              <li><a href=\"/community\" className=\"text-gray-300 hover:text-white\">社区</a></li>\n            </ul>\n          </div>\n\n          <div>\n            <h3 className=\"font-bold mb-4\">Legal</h3>\n            <ul className=\"space-y-2\">\n              <li><a href=\"/terms-of-service\" className=\"text-gray-300 hover:text-white\">条款</a></li>\n              <li><a href=\"/security\" className=\"text-gray-300 hover:text-white\">安全</a></li>\n              <li><a href=\"/privacy\" className=\"text-gray-300 hover:text-white\">隐私</a></li>\n            </ul>\n          </div>\n        </div>\n\n        {/* Bottom */}\n        <div className=\"border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center\">\n          <div className=\"text-gray-300 mb-4 md:mb-0\">\n            © 2025 Made by <a href=\"https://anysphere.inc\" className=\"hover:text-white\">Anysphere</a>\n          </div>\n          <div className=\"flex items-center space-x-4\">\n            <span className=\"text-gray-300\">Chinese</span>\n            <span className=\"text-gray-300\">SOC 2 Certified</span>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-white\">\n      <Navigation />\n      <HeroSection />\n      <CompanyTrustSection />\n      <FeaturesSection />\n      <ProductFeaturesSection />\n      <TestimonialsSection />\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA,uBAAuB;AACvB,SAAS;IACP,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAA+B;;;;;;;;;;;;;;;;;;;;;kCAMrD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,MAAK;oCAAW,WAAU;8CAA+D;;;;;;8CAG5F,8OAAC;oCAAE,MAAK;oCAAY,WAAU;8CAA+D;;;;;;8CAG7F,8OAAC;oCAAE,MAAK;oCAAc,WAAU;8CAA+D;;;;;;8CAG/F,8OAAC;oCAAE,MAAK;oCAAQ,WAAU;8CAA+D;;;;;;8CAGzF,8OAAC;oCAAE,MAAK;oCAA2B,WAAU;8CAA+D;;;;;;8CAG5G,8OAAC;oCAAE,MAAK;oCAAyB,WAAU;8CAA+D;;;;;;;;;;;;;;;;;kCAO9G,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,MAAK;gCAAkB,WAAU;0CAA+D;;;;;;0CAGnG,8OAAC;gCACC,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;AAEA,yBAAyB;AACzB,SAAS;IACP,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAEb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAgE;;;;;;8CAG9E,8OAAC;oCAAE,WAAU;8CAA2D;;;;;;8CAKxE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAO,WAAU;sDAAmG;;;;;;sDAGrH,8OAAC;4CAAE,MAAK;4CAAa,WAAU;sDAAmD;;;;;;;;;;;;8CAMpF,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS1D;AAEA,wBAAwB;AACxB,SAAS;IACP,MAAM,YAAY;QAChB;QAAqB;QAAU;QAAU;QAAW;QACpD;QAAc;QAAQ;QAAW;QAAY;KAC9C;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC;wBAAI,WAAU;kCACZ,UAAU,GAAG,CAAC,CAAC,SAAS,sBACvB,8OAAC;gCAAgB,WAAU;0CACxB;+BADO;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB;AAEA,mBAAmB;AACnB,SAAS;IACP,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAIzD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CAAwB;;;;;;;;;;;;;;;;;8BAK5C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAIzD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CAAwB;;;;;;;;;;;;;;;;;8BAK5C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAIzD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMpD;AAEA,iCAAiC;AACjC,SAAS;IACP,MAAM,WAAW;QACf;YACE,OAAO;YACP,aAAa;QACf;QACA;YACE,OAAO;YACP,aAAa;QACf;QACA;YACE,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,8OAAC;4BAAE,MAAK;4BAAY,WAAU;sCAAgD;;;;;;;;;;;;8BAKhF,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC;oCAAG,WAAU;8CAAwC,QAAQ,KAAK;;;;;;8CACnE,8OAAC;oCAAE,WAAU;8CAAiB,QAAQ,WAAW;;;;;;;2BAFzC;;;;;;;;;;;;;;;;;;;;;AAStB;AAEA,uBAAuB;AACvB,SAAS;IACP,MAAM,eAAe;QACnB;YACE,OAAO;YACP,QAAQ;YACR,SAAS;QACX;QACA;YACE,OAAO;YACP,QAAQ;YACR,SAAS;QACX;QACA;YACE,OAAO;YACP,QAAQ;YACR,SAAS;QACX;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAKvC,8OAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC;oCAAE,WAAU;;wCAA4B;wCAAE,YAAY,KAAK;wCAAC;;;;;;;8CAC7D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DAA2B,YAAY,MAAM;;;;;;8DAC5D,8OAAC;oDAAI,WAAU;8DAAiB,YAAY,OAAO;;;;;;;;;;;;;;;;;;;2BAN/C;;;;;;;;;;;;;;;;;;;;;AAetB;AAEA,mBAAmB;AACnB,SAAS;IACP,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAA+B;;;;;;;;;;;;;;;;sCAGnD,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCACpD,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAC1C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,MAAK;gCAAuB,WAAU;0CAAiC;;;;;;;;;;;sCAM5E,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,MAAK;oCAA0B,WAAU;8CAAiC;;;;;;8CAG7E,8OAAC;oCAAE,MAAK;oCAAsC,WAAU;8CAAiC;;;;;;8CAGzF,8OAAC;oCAAE,MAAK;oCAAkC,WAAU;8CAAiC;;;;;;8CAGrF,8OAAC;oCAAE,MAAK;oCAAqC,WAAU;8CAAiC;;;;;;;;;;;;;;;;;;8BAO5F,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAiB;;;;;;8CAC/B,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC;gDAAE,MAAK;gDAAW,WAAU;0DAAiC;;;;;;;;;;;sDAClE,8OAAC;sDAAG,cAAA,8OAAC;gDAAE,MAAK;gDAAY,WAAU;0DAAiC;;;;;;;;;;;sDACnE,8OAAC;sDAAG,cAAA,8OAAC;gDAAE,MAAK;gDAAc,WAAU;0DAAiC;;;;;;;;;;;sDACrE,8OAAC;sDAAG,cAAA,8OAAC;gDAAE,MAAK;gDAAa,WAAU;0DAAiC;;;;;;;;;;;sDACpE,8OAAC;sDAAG,cAAA,8OAAC;gDAAE,MAAK;gDAAY,WAAU;0DAAiC;;;;;;;;;;;;;;;;;;;;;;;sCAIvE,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAiB;;;;;;8CAC/B,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC;gDAAE,MAAK;gDAA0B,WAAU;0DAAiC;;;;;;;;;;;sDACjF,8OAAC;sDAAG,cAAA,8OAAC;gDAAE,MAAK;gDAAQ,WAAU;0DAAiC;;;;;;;;;;;sDAC/D,8OAAC;sDAAG,cAAA,8OAAC;gDAAE,MAAK;gDAA2B,WAAU;0DAAiC;;;;;;;;;;;sDAClF,8OAAC;sDAAG,cAAA,8OAAC;gDAAE,MAAK;gDAAa,WAAU;0DAAiC;;;;;;;;;;;;;;;;;;;;;;;sCAIxE,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAiB;;;;;;8CAC/B,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC;gDAAE,MAAK;gDAAwB,WAAU;0DAAiC;;;;;;;;;;;sDAC/E,8OAAC;sDAAG,cAAA,8OAAC;gDAAE,MAAK;gDAAwB,WAAU;0DAAiC;;;;;;;;;;;sDAC/E,8OAAC;sDAAG,cAAA,8OAAC;gDAAE,MAAK;gDAAa,WAAU;0DAAiC;;;;;;;;;;;;;;;;;;;;;;;sCAIxE,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAiB;;;;;;8CAC/B,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC;gDAAE,MAAK;gDAAoB,WAAU;0DAAiC;;;;;;;;;;;sDAC3E,8OAAC;sDAAG,cAAA,8OAAC;gDAAE,MAAK;gDAAY,WAAU;0DAAiC;;;;;;;;;;;sDACnE,8OAAC;sDAAG,cAAA,8OAAC;gDAAE,MAAK;gDAAW,WAAU;0DAAiC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAMxE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;gCAA6B;8CAC3B,8OAAC;oCAAE,MAAK;oCAAwB,WAAU;8CAAmB;;;;;;;;;;;;sCAE9E,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5C;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;;;;;0BACD,8OAAC;;;;;0BACD,8OAAC;;;;;0BACD,8OAAC;;;;;0BACD,8OAAC;;;;;0BACD,8OAAC;;;;;0BACD,8OAAC;;;;;;;;;;;AAGP"}}, {"offset": {"line": 1292, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1303, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 1303, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1309, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/T_02/cursor-clone/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind'\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR'\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: any\ndeclare const __next_app_load_chunk__: any\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base'\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AA0BA,8BAA8B;AAzB9B,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;AAYpI,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;AAWtB,OAAO,MAAMG,eAAe;AAG5B,EAAC;;;;;;;;;;AAED,cAAc,qCAAoC,sBAAA;AAElD,UAAA,kDAA4D;AAC5D,MAAA,CAAO,MAAMK;IAAAA;IAAAA,SAAc,IAAIX,mBAAmB;YAChDY,QAAAA;YAAAA,GAAY;YAAA;iBACVC,MAAMZ,UAAUa,QAAQ;sBACxBC,IAAAA,CAAM,CAAA;gBAAA,QAAA;oBAAA,IAAA;oBAAA;iBAAA;;eACNC,UAAU;;SACV,2CAA2C;cAC3CC,IAAAA;YAAAA,GAAY,GAAA;iBACZC,MAAAA,IAAU,IAAA;wBAAA;4BACVC,KAAAA,CAAAA,GAAAA,2KAAAA,CAAAA,KAAU,EAAE,eAAA,EAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,iTAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACd,OAAA,GAAA,iTAAA,CAAA,UAAA,CAAA,KAAA,CAAA,CAAA,EAAA,iTAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACAC,MAAU,CAAA,YAAA,CAAA;;qBACRC,YAAYnB;aACd;QACF,CAAE;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA", "ignoreList": [0]}}, {"offset": {"line": 1406, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}