{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/T_02/cursor-clone/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\n\nexport default function Home() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  return (\n    <div className=\"min-h-screen bg-white\">\n      {/* Navigation */}\n      <nav className=\"fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-md border-b border-gray-200 shadow-sm\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <div className=\"w-8 h-8 bg-black rounded-md flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">C</span>\n              </div>\n            </div>\n\n            <div className=\"hidden md:flex items-center space-x-8\">\n              <a href=\"/pricing\" className=\"text-gray-700 hover:text-black px-3 py-2 text-sm font-medium\">定价</a>\n              <a href=\"/features\" className=\"text-gray-700 hover:text-black px-3 py-2 text-sm font-medium\">功能</a>\n              <a href=\"/enterprise\" className=\"text-gray-700 hover:text-black px-3 py-2 text-sm font-medium\">企业</a>\n              <a href=\"/blog\" className=\"text-gray-700 hover:text-black px-3 py-2 text-sm font-medium\">博客</a>\n            </div>\n\n            <div className=\"hidden md:flex items-center space-x-4\">\n              <a href=\"/login\" className=\"text-gray-700 hover:text-black px-3 py-2 text-sm font-medium\">登录</a>\n              <a href=\"/downloads\" className=\"bg-black text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-800\">下载</a>\n            </div>\n\n            <div className=\"md:hidden\">\n              <button onClick={() => setIsMenuOpen(!isMenuOpen)} className=\"text-gray-700 hover:text-black p-2\">\n                <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  {isMenuOpen ? (\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  ) : (\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                  )}\n                </svg>\n              </button>\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* Hero Section */}\n      <div className=\"pt-32 pb-20 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"text-center\">\n            <div className=\"relative mb-12\">\n              <div className=\"absolute inset-0 bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 rounded-3xl opacity-20 blur-3xl\"></div>\n              <div className=\"relative bg-gradient-to-br from-gray-50 to-white rounded-3xl p-8 md:p-16 shadow-xl border border-gray-100\">\n                <h1 className=\"text-4xl md:text-6xl lg:text-7xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-6\">\n                  AI 代码编辑器\n                </h1>\n                <p className=\"text-xl md:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto\">\n                  旨在让你获得超凡的生产力， Cursor 是使用 AI 编写代码的最佳方式。\n                </p>\n                \n                <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center mb-12\">\n                  <button className=\"bg-black text-white px-8 py-4 rounded-lg text-lg font-medium hover:bg-gray-800 transition-all duration-300 hover:scale-105 shadow-lg\">\n                    下载 MacOS\n                  </button>\n                  <a href=\"/downloads\" className=\"text-gray-600 hover:text-black text-lg underline\">\n                    所有下载\n                  </a>\n                </div>\n\n                <div className=\"bg-gradient-to-br from-gray-900 via-gray-800 to-black rounded-xl h-64 md:h-96 flex items-center justify-center shadow-2xl border border-gray-700\">\n                  <div className=\"text-center\">\n                    <div className=\"w-16 h-16 bg-white rounded-lg flex items-center justify-center mx-auto mb-4\">\n                      <span className=\"text-black font-bold text-xl\">C</span>\n                    </div>\n                    <span className=\"text-gray-400 text-lg\">Cursor 编辑器界面预览</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Company Trust Section */}\n      <div className=\"py-16 px-4 sm:px-6 lg:px-8 bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-8\">由工程师信任</h2>\n            <div className=\"grid grid-cols-2 md:grid-cols-5 gap-8 items-center\">\n              {[\"Johnson & Johnson\", \"OpenAI\", \"Stripe\", \"Samsung\", \"Instacart\", \"Perplexity\", \"Ramp\", \"Shopify\", \"US Foods\", \"MercadoLibre\"].map((company, index) => (\n                <div key={index} className=\"text-gray-600 font-medium text-sm md:text-base\">\n                  {company}\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Features Section */}\n      <div className=\"py-20 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              更快构建软件\n            </h2>\n            <p className=\"text-xl text-gray-600 mb-8\">\n              智能、高速且熟悉，Cursor 是 AI 写代码的最佳选择。\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-3 gap-8\">\n            {[\n              { title: \"前沿智能\", description: \"由定制化模型与前沿模型混合驱动，Cursor 既聪明又快速。\" },\n              { title: \"熟悉的体验\", description: \"一键即可导入所有扩展、主题和快捷键绑定。\" },\n              { title: \"隐私选项\", description: \"启用隐私模式后，你的代码不会储存于远程。Cursor 已通过 SOC 2 认证。\" }\n            ].map((feature, index) => (\n              <div key={index} className=\"bg-white p-8 rounded-xl shadow-sm border border-gray-200 hover:shadow-lg transition-all duration-300\">\n                <h3 className=\"text-xl font-bold text-gray-900 mb-4\">{feature.title}</h3>\n                <p className=\"text-gray-600\">{feature.description}</p>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Footer */}\n      <footer className=\"bg-black text-white py-16 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"text-center mb-16\">\n            <div className=\"w-16 h-16 bg-white rounded-lg flex items-center justify-center mx-auto mb-6\">\n              <span className=\"text-black font-bold text-xl\">C</span>\n            </div>\n            <h2 className=\"text-3xl md:text-4xl font-bold mb-4\">立即试用 Cursor</h2>\n            <p className=\"text-xl text-gray-300 mb-8\">免费下载</p>\n          </div>\n\n          <div className=\"grid md:grid-cols-4 gap-8 mb-12\">\n            <div>\n              <h3 className=\"font-bold mb-4\">Product</h3>\n              <ul className=\"space-y-2\">\n                <li><a href=\"/pricing\" className=\"text-gray-300 hover:text-white\">定价</a></li>\n                <li><a href=\"/features\" className=\"text-gray-300 hover:text-white\">Features</a></li>\n                <li><a href=\"/downloads\" className=\"text-gray-300 hover:text-white\">下载</a></li>\n              </ul>\n            </div>\n            <div>\n              <h3 className=\"font-bold mb-4\">Resources</h3>\n              <ul className=\"space-y-2\">\n                <li><a href=\"/docs\" className=\"text-gray-300 hover:text-white\">文档</a></li>\n                <li><a href=\"/blog\" className=\"text-gray-300 hover:text-white\">Blog</a></li>\n                <li><a href=\"/forum\" className=\"text-gray-300 hover:text-white\">论坛</a></li>\n              </ul>\n            </div>\n            <div>\n              <h3 className=\"font-bold mb-4\">Company</h3>\n              <ul className=\"space-y-2\">\n                <li><a href=\"/about\" className=\"text-gray-300 hover:text-white\">公司</a></li>\n                <li><a href=\"/careers\" className=\"text-gray-300 hover:text-white\">招聘</a></li>\n              </ul>\n            </div>\n            <div>\n              <h3 className=\"font-bold mb-4\">Legal</h3>\n              <ul className=\"space-y-2\">\n                <li><a href=\"/terms\" className=\"text-gray-300 hover:text-white\">条款</a></li>\n                <li><a href=\"/privacy\" className=\"text-gray-300 hover:text-white\">隐私</a></li>\n              </ul>\n            </div>\n          </div>\n\n          <div className=\"border-t border-gray-800 pt-8 text-center\">\n            <div className=\"text-gray-300\">\n              © 2025 Made by Anysphere\n            </div>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;;;;;;0CAInD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,MAAK;wCAAW,WAAU;kDAA+D;;;;;;kDAC5F,8OAAC;wCAAE,MAAK;wCAAY,WAAU;kDAA+D;;;;;;kDAC7F,8OAAC;wCAAE,MAAK;wCAAc,WAAU;kDAA+D;;;;;;kDAC/F,8OAAC;wCAAE,MAAK;wCAAQ,WAAU;kDAA+D;;;;;;;;;;;;0CAG3F,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,MAAK;wCAAS,WAAU;kDAA+D;;;;;;kDAC1F,8OAAC;wCAAE,MAAK;wCAAa,WAAU;kDAAiF;;;;;;;;;;;;0CAGlH,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAO,SAAS,IAAM,cAAc,CAAC;oCAAa,WAAU;8CAC3D,cAAA,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,SAAQ;wCAAY,QAAO;kDAC7D,2BACC,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;iEAErE,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUnF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2H;;;;;;sDAGzI,8OAAC;4CAAE,WAAU;sDAA2D;;;;;;sDAIxE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAO,WAAU;8DAAuI;;;;;;8DAGzJ,8OAAC;oDAAE,MAAK;oDAAa,WAAU;8DAAmD;;;;;;;;;;;;sDAKpF,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEAA+B;;;;;;;;;;;kEAEjD,8OAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUtD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC;gCAAI,WAAU;0CACZ;oCAAC;oCAAqB;oCAAU;oCAAU;oCAAW;oCAAa;oCAAc;oCAAQ;oCAAW;oCAAY;iCAAe,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC5I,8OAAC;wCAAgB,WAAU;kDACxB;uCADO;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUpB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAK5C,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,OAAO;oCAAQ,aAAa;gCAAiC;gCAC/D;oCAAE,OAAO;oCAAS,aAAa;gCAAuB;gCACtD;oCAAE,OAAO;oCAAQ,aAAa;gCAA2C;6BAC1E,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC;4CAAG,WAAU;sDAAwC,QAAQ,KAAK;;;;;;sDACnE,8OAAC;4CAAE,WAAU;sDAAiB,QAAQ,WAAW;;;;;;;mCAFzC;;;;;;;;;;;;;;;;;;;;;0BAUlB,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAG5C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAiB;;;;;;sDAC/B,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG,cAAA,8OAAC;wDAAE,MAAK;wDAAW,WAAU;kEAAiC;;;;;;;;;;;8DAClE,8OAAC;8DAAG,cAAA,8OAAC;wDAAE,MAAK;wDAAY,WAAU;kEAAiC;;;;;;;;;;;8DACnE,8OAAC;8DAAG,cAAA,8OAAC;wDAAE,MAAK;wDAAa,WAAU;kEAAiC;;;;;;;;;;;;;;;;;;;;;;;8CAGxE,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAiB;;;;;;sDAC/B,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG,cAAA,8OAAC;wDAAE,MAAK;wDAAQ,WAAU;kEAAiC;;;;;;;;;;;8DAC/D,8OAAC;8DAAG,cAAA,8OAAC;wDAAE,MAAK;wDAAQ,WAAU;kEAAiC;;;;;;;;;;;8DAC/D,8OAAC;8DAAG,cAAA,8OAAC;wDAAE,MAAK;wDAAS,WAAU;kEAAiC;;;;;;;;;;;;;;;;;;;;;;;8CAGpE,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAiB;;;;;;sDAC/B,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG,cAAA,8OAAC;wDAAE,MAAK;wDAAS,WAAU;kEAAiC;;;;;;;;;;;8DAChE,8OAAC;8DAAG,cAAA,8OAAC;wDAAE,MAAK;wDAAW,WAAU;kEAAiC;;;;;;;;;;;;;;;;;;;;;;;8CAGtE,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAiB;;;;;;sDAC/B,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG,cAAA,8OAAC;wDAAE,MAAK;wDAAS,WAAU;kEAAiC;;;;;;;;;;;8DAChE,8OAAC;8DAAG,cAAA,8OAAC;wDAAE,MAAK;wDAAW,WAAU;kEAAiC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAKxE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3C"}}, {"offset": {"line": 796, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 806, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/T_02/cursor-clone/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n    } else if (process.env.TURBOPACK) {\n      module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n    } else {\n      module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n    } else if (process.env.TURBOPACK) {\n      module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n    } else {\n      module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAQzC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1CJ,OAAOC,OAAO,GAAGC,QAAQ;QAC3B,OAAO,IAAIL,QAAQC,GAAG,CAACO,SAAS,EAAE;;QAIlC;IACF;AACF", "ignoreList": [0]}}, {"offset": {"line": 820, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 825, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/T_02/cursor-clone/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,0HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0]}}, {"offset": {"line": 827, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 832, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/T_02/cursor-clone/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,0HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0]}}, {"offset": {"line": 834, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}