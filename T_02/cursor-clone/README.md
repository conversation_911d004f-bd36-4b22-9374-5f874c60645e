# Cursor 官网克隆版 - 完全优化版

这是一个完全复制 Cursor 官网 (https://www.cursor.com/cn) 的项目，使用 Next.js 和 Tailwind CSS 构建，并进行了全面的性能和用户体验优化。

## 🚀 功能特性

### 核心功能
- 🎨 **完全复制的设计**：与原版 Cursor 官网一模一样的视觉设计
- 📱 **响应式设计**：完美适配桌面端和移动端
- ⚡ **现代化技术栈**：使用 Next.js 15 + TypeScript + Tailwind CSS
- 🌟 **动画效果**：包含平滑的过渡动画和悬停效果
- 📱 **移动端菜单**：完整的移动端导航体验
- 🎯 **SEO 优化**：正确的元数据和页面标题

### 性能优化
- ⚡ **懒加载**：使用 Intersection Observer API 实现组件懒加载
- 🔄 **代码分割**：自动代码分割和按需加载
- 📊 **性能监控**：内置性能分析和 Web Vitals 监控
- 🛡️ **错误边界**：完整的错误处理和边界保护
- 🎨 **CSS 优化**：优化的样式加载和自定义动画

### 用户体验
- 📢 **通知横幅**：可配置的通知系统
- ⬆️ **回到顶部**：平滑滚动到顶部按钮
- 🎭 **加载状态**：优雅的加载动画和骨架屏
- 🔍 **搜索引擎优化**：完整的 SEO 元数据和结构化数据

## 页面内容

### 主要区块
1. **导航栏**：固定顶部导航，包含 Logo、菜单链接和操作按钮
2. **英雄区域**：主标题、描述文本和下载按钮
3. **公司信任标识**：展示使用 Cursor 的知名公司
4. **功能展示**：三个主要功能的详细介绍
   - Tab 补全功能
   - 代码库理解
   - 自然语言编辑
5. **产品特性卡片**：前沿智能、熟悉体验、隐私选项
6. **用户评价**：来自知名公司工程师的真实评价
7. **页脚**：完整的链接导航和社交媒体链接

### 技术实现
- **框架**：Next.js 15 (App Router)
- **样式**：Tailwind CSS + 自定义 CSS
- **字体**：Inter 字体 (Google Fonts)
- **图标**：SVG 图标
- **动画**：CSS 动画和过渡效果

## 快速开始

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看网站。

### 构建生产版本
```bash
npm run build
npm start
```

## 项目结构

```
cursor-clone/
├── src/
│   ├── app/
│   │   ├── globals.css      # 全局样式
│   │   ├── layout.tsx       # 根布局
│   │   └── page.tsx         # 主页面
│   └── ...
├── public/                  # 静态资源
├── tailwind.config.ts       # Tailwind 配置
└── package.json
```

## 🧩 组件架构

### 主要页面组件
- `Navigation`: 响应式导航栏组件（包含移动端菜单）
- `HeroSection`: 英雄区域组件（包含代码编辑器模拟界面）
- `CompanyTrustSection`: 公司信任标识组件
- `FeaturesSection`: 功能展示组件（包含图标和动画）
- `ProductFeaturesSection`: 产品特性卡片组件
- `TestimonialsSection`: 用户评价组件
- `Footer`: 页脚组件（包含社交媒体链接）

### 优化组件
- `LazySection`: 懒加载包装组件
- `LoadingSpinner`: 加载动画组件
- `ScrollToTop`: 回到顶部按钮组件
- `NotificationBanner`: 通知横幅组件
- `Analytics`: 性能监控组件
- `ErrorBoundary`: 错误边界组件
- `SEO`: SEO 优化组件

### 图标组件
- `CursorLogo`: Cursor Logo 组件
- `MenuIcon`, `CloseIcon`: 菜单图标
- `DownloadIcon`, `ArrowRightIcon`: 操作图标
- `CodeIcon`, `BrainIcon`, `ShieldIcon`, `SparkleIcon`: 功能图标
- `TwitterIcon`, `GitHubIcon`, `YouTubeIcon`, `RedditIcon`: 社交媒体图标

## 样式特性

- 使用 Inter 字体提供现代化的视觉体验
- 渐变背景和阴影效果
- 平滑的悬停动画
- 响应式网格布局
- 自定义滚动条样式

## 🔧 性能优化详情

### 懒加载实现
- 使用 `Intersection Observer API` 实现组件懒加载
- 只有当组件进入视口时才开始渲染
- 减少初始页面加载时间和资源消耗

### 代码分割
- Next.js 自动代码分割
- 按需加载组件和资源
- 减少首屏加载时间

### 性能监控
- 实时监控页面加载性能
- 追踪 Web Vitals 指标（FCP、LCP、CLS、FID、TTFB）
- 开发环境下输出详细性能报告

### CSS 优化
- 使用 Tailwind CSS 的 JIT 模式
- 自定义动画和过渡效果
- 优化的字体加载策略

### SEO 优化
- 完整的元数据配置
- 结构化数据标记
- 正确的 sitemap.xml 和 robots.txt
- 语义化 HTML 结构

## 📁 文件结构

```
src/
├── app/
│   ├── globals.css          # 全局样式和动画
│   ├── layout.tsx           # 根布局
│   └── page.tsx             # 主页面
├── components/
│   ├── icons.tsx            # 图标组件集合
│   ├── LazySection.tsx      # 懒加载组件
│   ├── LoadingSpinner.tsx   # 加载动画组件
│   ├── ScrollToTop.tsx      # 回到顶部组件
│   ├── NotificationBanner.tsx # 通知横幅组件
│   ├── Analytics.tsx        # 性能监控组件
│   └── SEO.tsx              # SEO 组件
public/
├── sitemap.xml              # 搜索引擎站点地图
├── robots.txt               # 搜索引擎爬虫规则
└── favicon.ico              # 网站图标
```

## 🚀 部署建议

### Vercel 部署
```bash
npm run build
vercel --prod
```

### 其他平台
```bash
npm run build
npm start
```

## 🔍 开发说明

这个项目完全基于原版 Cursor 官网进行复制和优化，包括：

### 完全复制的内容
- 所有文本内容（中文版本）
- 视觉设计和布局
- 交互效果和动画
- 响应式行为

### 额外优化
- 性能监控和分析
- 懒加载和代码分割
- 错误边界和异常处理
- SEO 优化和可访问性改进
- 现代化的开发工具链

项目使用现代化的 Web 开发技术栈，确保了良好的性能和用户体验。

## 📊 性能指标

在开发环境中，你可以在浏览器控制台看到以下性能指标：
- 页面加载时间
- DOM 内容加载时间
- 首次内容绘制 (FCP)
- 最大内容绘制 (LCP)
- 用户交互追踪

## 🛠️ 自定义配置

### 修改通知横幅
在 `src/app/page.tsx` 中修改 `NotificationBanner` 组件的 props：

```tsx
<NotificationBanner
  message="你的自定义消息"
  type="info" // info | success | warning | error
  actionText="了解更多"
  onAction={() => window.open('/your-link', '_blank')}
/>
```

### 添加新的动画
在 `src/app/globals.css` 中添加自定义 CSS 动画：

```css
@keyframes yourAnimation {
  from { /* 起始状态 */ }
  to { /* 结束状态 */ }
}

.your-animation-class {
  animation: yourAnimation 1s ease-in-out;
}
```

## 📄 许可证

本项目仅用于学习和演示目的。所有设计和内容版权归 Anysphere (Cursor) 所有。
