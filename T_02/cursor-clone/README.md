# Cursor 官网克隆版

这是一个完全复制 Cursor 官网 (https://www.cursor.com/cn) 的项目，使用 Next.js 和 Tailwind CSS 构建。

## 功能特性

- 🎨 **完全复制的设计**：与原版 Cursor 官网一模一样的视觉设计
- 📱 **响应式设计**：完美适配桌面端和移动端
- ⚡ **现代化技术栈**：使用 Next.js 15 + TypeScript + Tailwind CSS
- 🌟 **动画效果**：包含平滑的过渡动画和悬停效果
- 📱 **移动端菜单**：完整的移动端导航体验
- 🎯 **SEO 优化**：正确的元数据和页面标题

## 页面内容

### 主要区块
1. **导航栏**：固定顶部导航，包含 Logo、菜单链接和操作按钮
2. **英雄区域**：主标题、描述文本和下载按钮
3. **公司信任标识**：展示使用 Cursor 的知名公司
4. **功能展示**：三个主要功能的详细介绍
   - Tab 补全功能
   - 代码库理解
   - 自然语言编辑
5. **产品特性卡片**：前沿智能、熟悉体验、隐私选项
6. **用户评价**：来自知名公司工程师的真实评价
7. **页脚**：完整的链接导航和社交媒体链接

### 技术实现
- **框架**：Next.js 15 (App Router)
- **样式**：Tailwind CSS + 自定义 CSS
- **字体**：Inter 字体 (Google Fonts)
- **图标**：SVG 图标
- **动画**：CSS 动画和过渡效果

## 快速开始

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看网站。

### 构建生产版本
```bash
npm run build
npm start
```

## 项目结构

```
cursor-clone/
├── src/
│   ├── app/
│   │   ├── globals.css      # 全局样式
│   │   ├── layout.tsx       # 根布局
│   │   └── page.tsx         # 主页面
│   └── ...
├── public/                  # 静态资源
├── tailwind.config.ts       # Tailwind 配置
└── package.json
```

## 主要组件

- `Navigation`: 响应式导航栏组件
- `HeroSection`: 英雄区域组件
- `CompanyTrustSection`: 公司信任标识组件
- `FeaturesSection`: 功能展示组件
- `ProductFeaturesSection`: 产品特性卡片组件
- `TestimonialsSection`: 用户评价组件
- `Footer`: 页脚组件

## 样式特性

- 使用 Inter 字体提供现代化的视觉体验
- 渐变背景和阴影效果
- 平滑的悬停动画
- 响应式网格布局
- 自定义滚动条样式

## 开发说明

这个项目完全基于原版 Cursor 官网进行复制，包括：
- 所有文本内容（中文版本）
- 视觉设计和布局
- 交互效果和动画
- 响应式行为

项目使用现代化的 Web 开发技术栈，确保了良好的性能和用户体验。

## 许可证

本项目仅用于学习和演示目的。
